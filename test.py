#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
淘宝逛逛话题自动输入测试脚本
"""

import time
import pyautogui
import keyboard
from pynput import mouse
from pynput.mouse import <PERSON><PERSON>, Listener

class TaobaoTopicTester:
    def __init__(self):
        self.editor_position = None
        self.is_recording = False
        
        # 设置pyautogui的安全设置
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.1
        
    def record_editor_position(self):
        """记录编辑框位置"""
        print("请点击淘宝逛逛的编辑框来记录位置...")
        print("点击后会自动记录坐标")
        
        def on_click(x, y, button, pressed):
            if pressed and button == Button.left:
                self.editor_position = (x, y)
                print(f"已记录编辑框位置: ({x}, {y})")
                return False  # 停止监听
        
        with Listener(on_click=on_click) as listener:
            listener.join()
    
    def click_editor(self):
        """点击编辑框"""
        if self.editor_position:
            pyautogui.click(self.editor_position[0], self.editor_position[1])
            time.sleep(0.2)
            print("已点击编辑框")
        else:
            print("请先记录编辑框位置！")
    
    def insert_topic_method1(self, topic_name):
        """方法1：使用Shift+3快捷键"""
        print(f"方法1：插入话题 '{topic_name}'")
        
        # 1. 点击编辑框
        self.click_editor()
        
        # 2. 按Shift+3
        pyautogui.keyDown('shift')
        pyautogui.press('3')
        pyautogui.keyUp('shift')
        time.sleep(0.5)
        print("已按下Shift+3")
        
        # 3. 输入话题名称
        pyautogui.write(topic_name, interval=0.05)
        time.sleep(0.3)
        print(f"已输入话题名称: {topic_name}")
        
        # 4. 按回车确认
        pyautogui.press('enter')
        time.sleep(0.3)
        print("已按回车确认")
    
    def insert_topic_method2(self, topic_name):
        """方法2：先输入#号再输入内容（支持中文）"""
        print(f"方法2：插入话题 '{topic_name}'")
        
        # 1. 点击编辑框
        self.click_editor()
        
        # 2. 直接输入#号
        pyautogui.write('#', interval=0.05)
        time.sleep(0.5)
        print("已输入#号")
        
        # 3. 使用剪贴板输入中文话题名称
        import pyperclip
        pyperclip.copy(topic_name)
        pyautogui.hotkey('ctrl', 'v')
        time.sleep(0.3)
        print(f"已输入话题名称: {topic_name}")
        
        # 4. 按空格或回车
        pyautogui.press('space')
        time.sleep(0.3)
        print("已按空格")
    
    def insert_multiple_topics(self, topics):
        """插入多个话题"""
        print(f"准备插入{len(topics)}个话题...")
        
        for i, topic in enumerate(topics):
            print(f"\n--- 插入第{i+1}个话题 ---")
            self.insert_topic_method1(topic)
            
            # 在话题之间添加一些文字
            pyautogui.write(f" 这是第{i+1}个话题测试 ", interval=0.05)
            time.sleep(0.5)
    
    def test_keyboard_input(self):
        """测试键盘输入功能"""
        print("测试键盘输入...")
        self.click_editor()
        
        test_text = "这是一个测试文本"
        pyautogui.write(test_text, interval=0.05)
        print(f"已输入测试文本: {test_text}")

def main():
    tester = TaobaoTopicTester()
    
    while True:
        print("\n" + "="*50)
        print("淘宝逛逛话题自动输入测试")
        print("="*50)
        print("1. 记录编辑框位置")
        print("2. 测试键盘输入")
        print("3. 方法1测试（Shift+3）")
        print("4. 方法2测试（直接输入#）")
        print("5. 批量插入话题测试")
        print("6. 退出")
        
        choice = input("\n请选择测试项目 (1-6): ").strip()
        
        if choice == "1":
            tester.record_editor_position()
            
        elif choice == "2":
            if tester.editor_position:
                print("3秒后开始测试，请准备...")
                time.sleep(3)
                tester.test_keyboard_input()
            else:
                print("请先记录编辑框位置！")
                
        elif choice == "3":
            if tester.editor_position:
                topic = input("请输入要测试的话题名称: ").strip()
                if topic:
                    print("3秒后开始测试，请准备...")
                    time.sleep(3)
                    tester.insert_topic_method1(topic)
            else:
                print("请先记录编辑框位置！")
                
        elif choice == "4":
            if tester.editor_position:
                topic = input("请输入要测试的话题名称: ").strip()
                if topic:
                    print("3秒后开始测试，请准备...")
                    time.sleep(3)
                    tester.insert_topic_method2(topic)
            else:
                print("请先记录编辑框位置！")
                
        elif choice == "5":
            if tester.editor_position:
                topics = ["好物分享", "测试话题", "魔芋爽"]
                print("3秒后开始批量测试，请准备...")
                time.sleep(3)
                tester.insert_multiple_topics(topics)
            else:
                print("请先记录编辑框位置！")
                
        elif choice == "6":
            print("退出测试程序")
            break
            
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    print("请先安装依赖包:")
    print("pip install pyautogui pynput")
    print("\n注意：")
    print("1. 请确保淘宝逛逛页面已打开")
    print("2. 测试时不要移动鼠标")
    print("3. 如果出错，快速移动鼠标到屏幕角落可以停止")
    
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n程序出错: {e}")