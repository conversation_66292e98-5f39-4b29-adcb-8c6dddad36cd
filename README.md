# 内容管理器 GUI 版本

这是一个带有现代化图形界面的内容管理器，可以从豆包API获取内容并通过快捷键进行粘贴操作。

## 版本说明

- **Flet版本** (推荐): 使用现代化的Flet框架，界面美观，基于Flutter技术
- **Tkinter版本**: 传统的Python GUI界面，功能完整但界面较为简单

## 功能特性

- 🖥️ **图形界面**: 直观的GUI界面，易于操作
- 📝 **自定义内容**: 可以在界面中自定义API请求的内容
- ⌨️ **自定义快捷键**: 可以设置任意快捷键进行粘贴操作
- 📊 **内容预览**: 实时显示获取到的内容列表
- 📋 **日志记录**: 所有操作都会记录到日志文件中
- 🔄 **自动补充**: 内容不足时自动获取新内容

## 安装依赖

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install flet keyboard pyperclip volcengine-python-sdk
```

## 使用方法

### 启动程序

**Flet版本 (推荐)**:
```bash
python run_flet.py
```

**Tkinter版本**:
```bash
python run_gui.py
```

### 界面说明

1. **配置区域**:
   - **API请求内容**: 输入要发送给API的内容（默认为"魔芋爽"）
   - **快捷键**: 设置触发粘贴的快捷键（默认为"f1"）

2. **控制按钮**:
   - **启动监听**: 开始监听设置的快捷键
   - **停止监听**: 停止快捷键监听
   - **获取内容**: 手动从API获取新内容
   - **测试粘贴**: 测试粘贴功能

3. **状态区域**:
   - 显示当前监听状态和内容数量

4. **内容预览**:
   - 显示已获取的所有内容（标题和文案）

5. **日志区域**:
   - 实时显示程序运行日志

### 使用流程

1. 在"API请求内容"框中输入你想要的内容（如"魔芋爽"）
2. 在"快捷键"框中设置你想要的快捷键（如"f1", "ctrl+space", "alt+v"等）
3. 点击"启动监听"按钮
4. 程序会自动获取内容并开始监听快捷键
5. 在任何地方按下设置的快捷键，程序会自动粘贴下一个内容
6. 内容会按照"标题 -> 文案 -> 标题 -> 文案"的顺序循环粘贴

## 快捷键格式

支持以下快捷键格式：
- 单个键：`f1`, `f2`, `space`, `enter` 等
- 组合键：`ctrl+c`, `alt+v`, `shift+f1` 等
- 多个修饰键：`ctrl+alt+v`, `ctrl+shift+space` 等

## 日志文件

程序会在 `logs/` 目录下创建日志文件，文件名格式为：
`content_manager_YYYYMMDD.log`

日志包含：
- 程序启动/停止信息
- API请求和响应信息
- 粘贴操作记录
- 错误信息

## 文件说明

- `content_manager_flet.py`: Flet版本的主程序（推荐）
- `content_manager_gui.py`: Tkinter版本的主程序
- `content_manager.py`: 原始的命令行版本
- `run_flet.py`: Flet版本的启动脚本
- `run_gui.py`: Tkinter版本的启动脚本
- `requirements.txt`: 依赖包列表
- `logs/`: 日志文件目录（自动创建）

## Flet版本特色

- 🎨 **现代化界面**: 基于Flutter技术，界面美观现代
- 📱 **响应式设计**: 自适应不同屏幕尺寸
- 🎯 **直观操作**: 图标按钮，颜色编码的状态显示
- 📊 **实时更新**: 内容和日志实时刷新
- 🔄 **流畅动画**: 平滑的界面过渡效果

## 注意事项

1. 确保有网络连接以访问豆包API
2. 某些快捷键可能与系统快捷键冲突，请选择合适的组合
3. 程序需要剪贴板权限来执行粘贴操作
4. 在某些应用中，自动粘贴可能需要额外的权限设置

## 故障排除

### 快捷键不工作
- 检查快捷键是否与系统快捷键冲突
- 尝试使用不同的快捷键组合
- 确保程序有足够的系统权限

### API请求失败
- 检查网络连接
- 确认API密钥是否正确
- 查看日志文件获取详细错误信息

### 粘贴不工作
- 确保目标应用支持Ctrl+V粘贴
- 检查剪贴板权限
- 尝试手动测试粘贴功能
