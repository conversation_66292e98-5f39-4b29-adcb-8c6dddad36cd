('D:\\Doubao_api\\build\\content_manager_flet\\PYZ-00.pyz',
 [('PIL',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PyQt5',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('__future__', 'D:\\Python\\Python39-32\\lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'D:\\Python\\Python39-32\\lib\\_aix_support.py', 'PYMODULE'),
  ('_bootsubprocess',
   'D:\\Python\\Python39-32\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\Python\\Python39-32\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression', 'D:\\Python\\Python39-32\\lib\\_compression.py', 'PYMODULE'),
  ('_distutils_hack',
   'D:\\Python\\Python39-32\\lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\Python\\Python39-32\\lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_osx_support', 'D:\\Python\\Python39-32\\lib\\_osx_support.py', 'PYMODULE'),
  ('_py_abc', 'D:\\Python\\Python39-32\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\Python\\Python39-32\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'D:\\Python\\Python39-32\\lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime', 'D:\\Python\\Python39-32\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\Python\\Python39-32\\lib\\_threading_local.py',
   'PYMODULE'),
  ('annotated_types',
   'D:\\Python\\Python39-32\\lib\\site-packages\\annotated_types\\__init__.py',
   'PYMODULE'),
  ('anyio',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\__init__.py',
   'PYMODULE'),
  ('anyio._backends',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\_backends\\__init__.py',
   'PYMODULE'),
  ('anyio._backends._asyncio',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\_backends\\_asyncio.py',
   'PYMODULE'),
  ('anyio._backends._trio',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\_backends\\_trio.py',
   'PYMODULE'),
  ('anyio._core',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\_core\\__init__.py',
   'PYMODULE'),
  ('anyio._core._asyncio_selector_thread',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\_core\\_asyncio_selector_thread.py',
   'PYMODULE'),
  ('anyio._core._eventloop',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\_core\\_eventloop.py',
   'PYMODULE'),
  ('anyio._core._exceptions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\_core\\_exceptions.py',
   'PYMODULE'),
  ('anyio._core._fileio',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\_core\\_fileio.py',
   'PYMODULE'),
  ('anyio._core._resources',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\_core\\_resources.py',
   'PYMODULE'),
  ('anyio._core._signals',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\_core\\_signals.py',
   'PYMODULE'),
  ('anyio._core._sockets',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\_core\\_sockets.py',
   'PYMODULE'),
  ('anyio._core._streams',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\_core\\_streams.py',
   'PYMODULE'),
  ('anyio._core._subprocesses',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\_core\\_subprocesses.py',
   'PYMODULE'),
  ('anyio._core._synchronization',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\_core\\_synchronization.py',
   'PYMODULE'),
  ('anyio._core._tasks',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\_core\\_tasks.py',
   'PYMODULE'),
  ('anyio._core._tempfile',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\_core\\_tempfile.py',
   'PYMODULE'),
  ('anyio._core._testing',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\_core\\_testing.py',
   'PYMODULE'),
  ('anyio._core._typedattr',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\_core\\_typedattr.py',
   'PYMODULE'),
  ('anyio.abc',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\abc\\__init__.py',
   'PYMODULE'),
  ('anyio.abc._eventloop',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\abc\\_eventloop.py',
   'PYMODULE'),
  ('anyio.abc._resources',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\abc\\_resources.py',
   'PYMODULE'),
  ('anyio.abc._sockets',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\abc\\_sockets.py',
   'PYMODULE'),
  ('anyio.abc._streams',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\abc\\_streams.py',
   'PYMODULE'),
  ('anyio.abc._subprocesses',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\abc\\_subprocesses.py',
   'PYMODULE'),
  ('anyio.abc._tasks',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\abc\\_tasks.py',
   'PYMODULE'),
  ('anyio.abc._testing',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\abc\\_testing.py',
   'PYMODULE'),
  ('anyio.from_thread',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\from_thread.py',
   'PYMODULE'),
  ('anyio.lowlevel',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\lowlevel.py',
   'PYMODULE'),
  ('anyio.streams',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\streams\\__init__.py',
   'PYMODULE'),
  ('anyio.streams.memory',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\streams\\memory.py',
   'PYMODULE'),
  ('anyio.streams.stapled',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\streams\\stapled.py',
   'PYMODULE'),
  ('anyio.streams.tls',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\streams\\tls.py',
   'PYMODULE'),
  ('anyio.to_thread',
   'D:\\Python\\Python39-32\\lib\\site-packages\\anyio\\to_thread.py',
   'PYMODULE'),
  ('argparse', 'D:\\Python\\Python39-32\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\Python\\Python39-32\\lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'D:\\Python\\Python39-32\\lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Python\\Python39-32\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Python\\Python39-32\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Python\\Python39-32\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\Python\\Python39-32\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\Python\\Python39-32\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\Python\\Python39-32\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\Python\\Python39-32\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\Python\\Python39-32\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Python\\Python39-32\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\Python\\Python39-32\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\Python\\Python39-32\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log', 'D:\\Python\\Python39-32\\lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Python\\Python39-32\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\Python\\Python39-32\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\Python\\Python39-32\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\Python\\Python39-32\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Python\\Python39-32\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\Python\\Python39-32\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\Python\\Python39-32\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\Python\\Python39-32\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\Python\\Python39-32\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\Python\\Python39-32\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\Python\\Python39-32\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\Python\\Python39-32\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\Python\\Python39-32\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Python\\Python39-32\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Python\\Python39-32\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Python\\Python39-32\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('attr',
   'D:\\Python\\Python39-32\\lib\\site-packages\\attr\\__init__.py',
   'PYMODULE'),
  ('attr._cmp',
   'D:\\Python\\Python39-32\\lib\\site-packages\\attr\\_cmp.py',
   'PYMODULE'),
  ('attr._compat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._config',
   'D:\\Python\\Python39-32\\lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('attr._funcs',
   'D:\\Python\\Python39-32\\lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._make',
   'D:\\Python\\Python39-32\\lib\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr._next_gen',
   'D:\\Python\\Python39-32\\lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._version_info',
   'D:\\Python\\Python39-32\\lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr.converters',
   'D:\\Python\\Python39-32\\lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.filters',
   'D:\\Python\\Python39-32\\lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.setters',
   'D:\\Python\\Python39-32\\lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr.validators',
   'D:\\Python\\Python39-32\\lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('attrs',
   'D:\\Python\\Python39-32\\lib\\site-packages\\attrs\\__init__.py',
   'PYMODULE'),
  ('attrs.converters',
   'D:\\Python\\Python39-32\\lib\\site-packages\\attrs\\converters.py',
   'PYMODULE'),
  ('attrs.exceptions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\attrs\\exceptions.py',
   'PYMODULE'),
  ('attrs.filters',
   'D:\\Python\\Python39-32\\lib\\site-packages\\attrs\\filters.py',
   'PYMODULE'),
  ('attrs.setters',
   'D:\\Python\\Python39-32\\lib\\site-packages\\attrs\\setters.py',
   'PYMODULE'),
  ('attrs.validators',
   'D:\\Python\\Python39-32\\lib\\site-packages\\attrs\\validators.py',
   'PYMODULE'),
  ('base64', 'D:\\Python\\Python39-32\\lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\Python\\Python39-32\\lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\Python\\Python39-32\\lib\\bisect.py', 'PYMODULE'),
  ('blinker',
   'D:\\Python\\Python39-32\\lib\\site-packages\\blinker\\__init__.py',
   'PYMODULE'),
  ('blinker._utilities',
   'D:\\Python\\Python39-32\\lib\\site-packages\\blinker\\_utilities.py',
   'PYMODULE'),
  ('blinker.base',
   'D:\\Python\\Python39-32\\lib\\site-packages\\blinker\\base.py',
   'PYMODULE'),
  ('brotli',
   'D:\\Python\\Python39-32\\lib\\site-packages\\brotli.py',
   'PYMODULE'),
  ('bz2', 'D:\\Python\\Python39-32\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\Python\\Python39-32\\lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'D:\\Python\\Python39-32\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\Python\\Python39-32\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cgi', 'D:\\Python\\Python39-32\\lib\\cgi.py', 'PYMODULE'),
  ('click',
   'D:\\Python\\Python39-32\\lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click._compat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'D:\\Python\\Python39-32\\lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'D:\\Python\\Python39-32\\lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'D:\\Python\\Python39-32\\lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core',
   'D:\\Python\\Python39-32\\lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'D:\\Python\\Python39-32\\lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'D:\\Python\\Python39-32\\lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'D:\\Python\\Python39-32\\lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'D:\\Python\\Python39-32\\lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'D:\\Python\\Python39-32\\lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'D:\\Python\\Python39-32\\lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.types',
   'D:\\Python\\Python39-32\\lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('cmd', 'D:\\Python\\Python39-32\\lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\Python\\Python39-32\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\Python\\Python39-32\\lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'D:\\Python\\Python39-32\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'D:\\Python\\Python39-32\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'D:\\Python\\Python39-32\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'D:\\Python\\Python39-32\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'D:\\Python\\Python39-32\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys', 'D:\\Python\\Python39-32\\lib\\colorsys.py', 'PYMODULE'),
  ('concurrent',
   'D:\\Python\\Python39-32\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\Python\\Python39-32\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Python\\Python39-32\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Python\\Python39-32\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Python\\Python39-32\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'D:\\Python\\Python39-32\\lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'D:\\Python\\Python39-32\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\Python\\Python39-32\\lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\Python\\Python39-32\\lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hmac',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf.hkdf',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\hkdf.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv', 'D:\\Python\\Python39-32\\lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\Python\\Python39-32\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\Python\\Python39-32\\lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian',
   'D:\\Python\\Python39-32\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\Python\\Python39-32\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\Python\\Python39-32\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\Python\\Python39-32\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\Python\\Python39-32\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'D:\\Python\\Python39-32\\lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\Python\\Python39-32\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses', 'D:\\Python\\Python39-32\\lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\Python\\Python39-32\\lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'D:\\Python\\Python39-32\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'D:\\Python\\Python39-32\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'D:\\Python\\Python39-32\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'D:\\Python\\Python39-32\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'D:\\Python\\Python39-32\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'D:\\Python\\Python39-32\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'D:\\Python\\Python39-32\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'D:\\Python\\Python39-32\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'D:\\Python\\Python39-32\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'D:\\Python\\Python39-32\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'D:\\Python\\Python39-32\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'D:\\Python\\Python39-32\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'D:\\Python\\Python39-32\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'D:\\Python\\Python39-32\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'D:\\Python\\Python39-32\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal', 'D:\\Python\\Python39-32\\lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\Python\\Python39-32\\lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\Python\\Python39-32\\lib\\dis.py', 'PYMODULE'),
  ('distutils',
   'D:\\Python\\Python39-32\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'D:\\Python\\Python39-32\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'D:\\Python\\Python39-32\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'D:\\Python\\Python39-32\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd',
   'D:\\Python\\Python39-32\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.command',
   'D:\\Python\\Python39-32\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'D:\\Python\\Python39-32\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'D:\\Python\\Python39-32\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'D:\\Python\\Python39-32\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config',
   'D:\\Python\\Python39-32\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'D:\\Python\\Python39-32\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.debug',
   'D:\\Python\\Python39-32\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'D:\\Python\\Python39-32\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'D:\\Python\\Python39-32\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.dist',
   'D:\\Python\\Python39-32\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.errors',
   'D:\\Python\\Python39-32\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.extension',
   'D:\\Python\\Python39-32\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'D:\\Python\\Python39-32\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'D:\\Python\\Python39-32\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'D:\\Python\\Python39-32\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log',
   'D:\\Python\\Python39-32\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'D:\\Python\\Python39-32\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.spawn',
   'D:\\Python\\Python39-32\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'D:\\Python\\Python39-32\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'D:\\Python\\Python39-32\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.util',
   'D:\\Python\\Python39-32\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.version',
   'D:\\Python\\Python39-32\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'D:\\Python\\Python39-32\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('doctest', 'D:\\Python\\Python39-32\\lib\\doctest.py', 'PYMODULE'),
  ('dotenv',
   'D:\\Python\\Python39-32\\lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'D:\\Python\\Python39-32\\lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'D:\\Python\\Python39-32\\lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.parser',
   'D:\\Python\\Python39-32\\lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   'D:\\Python\\Python39-32\\lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('email', 'D:\\Python\\Python39-32\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\Python\\Python39-32\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Python\\Python39-32\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Python\\Python39-32\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Python\\Python39-32\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Python\\Python39-32\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\Python\\Python39-32\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Python\\Python39-32\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Python\\Python39-32\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Python\\Python39-32\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\Python\\Python39-32\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Python\\Python39-32\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Python\\Python39-32\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\Python\\Python39-32\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Python\\Python39-32\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Python\\Python39-32\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\Python\\Python39-32\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Python\\Python39-32\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Python\\Python39-32\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils', 'D:\\Python\\Python39-32\\lib\\email\\utils.py', 'PYMODULE'),
  ('exceptiongroup',
   'D:\\Python\\Python39-32\\lib\\site-packages\\exceptiongroup\\__init__.py',
   'PYMODULE'),
  ('exceptiongroup._catch',
   'D:\\Python\\Python39-32\\lib\\site-packages\\exceptiongroup\\_catch.py',
   'PYMODULE'),
  ('exceptiongroup._exceptions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\exceptiongroup\\_exceptions.py',
   'PYMODULE'),
  ('exceptiongroup._formatting',
   'D:\\Python\\Python39-32\\lib\\site-packages\\exceptiongroup\\_formatting.py',
   'PYMODULE'),
  ('exceptiongroup._suppress',
   'D:\\Python\\Python39-32\\lib\\site-packages\\exceptiongroup\\_suppress.py',
   'PYMODULE'),
  ('exceptiongroup._version',
   'D:\\Python\\Python39-32\\lib\\site-packages\\exceptiongroup\\_version.py',
   'PYMODULE'),
  ('flet',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\__init__.py',
   'PYMODULE'),
  ('flet.app',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\app.py',
   'PYMODULE'),
  ('flet.auth',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\auth\\__init__.py',
   'PYMODULE'),
  ('flet.auth.authorization',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\auth\\authorization.py',
   'PYMODULE'),
  ('flet.auth.group',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\auth\\group.py',
   'PYMODULE'),
  ('flet.auth.oauth_provider',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\auth\\oauth_provider.py',
   'PYMODULE'),
  ('flet.auth.oauth_token',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\auth\\oauth_token.py',
   'PYMODULE'),
  ('flet.auth.user',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\auth\\user.py',
   'PYMODULE'),
  ('flet.core', '-', 'PYMODULE'),
  ('flet.core.adaptive_control',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\adaptive_control.py',
   'PYMODULE'),
  ('flet.core.alert_dialog',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\alert_dialog.py',
   'PYMODULE'),
  ('flet.core.alignment',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\alignment.py',
   'PYMODULE'),
  ('flet.core.animated_switcher',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\animated_switcher.py',
   'PYMODULE'),
  ('flet.core.animation',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\animation.py',
   'PYMODULE'),
  ('flet.core.app_bar',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\app_bar.py',
   'PYMODULE'),
  ('flet.core.audio',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\audio.py',
   'PYMODULE'),
  ('flet.core.audio_recorder',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\audio_recorder.py',
   'PYMODULE'),
  ('flet.core.auto_complete',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\auto_complete.py',
   'PYMODULE'),
  ('flet.core.autofill_group',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\autofill_group.py',
   'PYMODULE'),
  ('flet.core.badge',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\badge.py',
   'PYMODULE'),
  ('flet.core.banner',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\banner.py',
   'PYMODULE'),
  ('flet.core.blur',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\blur.py',
   'PYMODULE'),
  ('flet.core.border',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\border.py',
   'PYMODULE'),
  ('flet.core.border_radius',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\border_radius.py',
   'PYMODULE'),
  ('flet.core.bottom_app_bar',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\bottom_app_bar.py',
   'PYMODULE'),
  ('flet.core.bottom_sheet',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\bottom_sheet.py',
   'PYMODULE'),
  ('flet.core.box',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\box.py',
   'PYMODULE'),
  ('flet.core.button',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\button.py',
   'PYMODULE'),
  ('flet.core.buttons',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\buttons.py',
   'PYMODULE'),
  ('flet.core.card',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\card.py',
   'PYMODULE'),
  ('flet.core.charts', '-', 'PYMODULE'),
  ('flet.core.charts.bar_chart',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\charts\\bar_chart.py',
   'PYMODULE'),
  ('flet.core.charts.bar_chart_group',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\charts\\bar_chart_group.py',
   'PYMODULE'),
  ('flet.core.charts.bar_chart_rod',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\charts\\bar_chart_rod.py',
   'PYMODULE'),
  ('flet.core.charts.bar_chart_rod_stack_item',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\charts\\bar_chart_rod_stack_item.py',
   'PYMODULE'),
  ('flet.core.charts.chart_axis',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\charts\\chart_axis.py',
   'PYMODULE'),
  ('flet.core.charts.chart_axis_label',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\charts\\chart_axis_label.py',
   'PYMODULE'),
  ('flet.core.charts.chart_grid_lines',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\charts\\chart_grid_lines.py',
   'PYMODULE'),
  ('flet.core.charts.chart_point_line',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\charts\\chart_point_line.py',
   'PYMODULE'),
  ('flet.core.charts.chart_point_shape',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\charts\\chart_point_shape.py',
   'PYMODULE'),
  ('flet.core.charts.line_chart',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\charts\\line_chart.py',
   'PYMODULE'),
  ('flet.core.charts.line_chart_data',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\charts\\line_chart_data.py',
   'PYMODULE'),
  ('flet.core.charts.line_chart_data_point',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\charts\\line_chart_data_point.py',
   'PYMODULE'),
  ('flet.core.charts.pie_chart',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\charts\\pie_chart.py',
   'PYMODULE'),
  ('flet.core.charts.pie_chart_section',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\charts\\pie_chart_section.py',
   'PYMODULE'),
  ('flet.core.checkbox',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\checkbox.py',
   'PYMODULE'),
  ('flet.core.chip',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\chip.py',
   'PYMODULE'),
  ('flet.core.circle_avatar',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\circle_avatar.py',
   'PYMODULE'),
  ('flet.core.client_storage',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\client_storage.py',
   'PYMODULE'),
  ('flet.core.colors',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\colors.py',
   'PYMODULE'),
  ('flet.core.column',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\column.py',
   'PYMODULE'),
  ('flet.core.connection',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\connection.py',
   'PYMODULE'),
  ('flet.core.constrained_control',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\constrained_control.py',
   'PYMODULE'),
  ('flet.core.container',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\container.py',
   'PYMODULE'),
  ('flet.core.control',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\control.py',
   'PYMODULE'),
  ('flet.core.control_event',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\control_event.py',
   'PYMODULE'),
  ('flet.core.cupertino_action_sheet',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\cupertino_action_sheet.py',
   'PYMODULE'),
  ('flet.core.cupertino_action_sheet_action',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\cupertino_action_sheet_action.py',
   'PYMODULE'),
  ('flet.core.cupertino_activity_indicator',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\cupertino_activity_indicator.py',
   'PYMODULE'),
  ('flet.core.cupertino_alert_dialog',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\cupertino_alert_dialog.py',
   'PYMODULE'),
  ('flet.core.cupertino_app_bar',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\cupertino_app_bar.py',
   'PYMODULE'),
  ('flet.core.cupertino_bottom_sheet',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\cupertino_bottom_sheet.py',
   'PYMODULE'),
  ('flet.core.cupertino_button',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\cupertino_button.py',
   'PYMODULE'),
  ('flet.core.cupertino_checkbox',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\cupertino_checkbox.py',
   'PYMODULE'),
  ('flet.core.cupertino_colors',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\cupertino_colors.py',
   'PYMODULE'),
  ('flet.core.cupertino_context_menu',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\cupertino_context_menu.py',
   'PYMODULE'),
  ('flet.core.cupertino_context_menu_action',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\cupertino_context_menu_action.py',
   'PYMODULE'),
  ('flet.core.cupertino_date_picker',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\cupertino_date_picker.py',
   'PYMODULE'),
  ('flet.core.cupertino_dialog_action',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\cupertino_dialog_action.py',
   'PYMODULE'),
  ('flet.core.cupertino_filled_button',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\cupertino_filled_button.py',
   'PYMODULE'),
  ('flet.core.cupertino_icons',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\cupertino_icons.py',
   'PYMODULE'),
  ('flet.core.cupertino_list_tile',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\cupertino_list_tile.py',
   'PYMODULE'),
  ('flet.core.cupertino_navigation_bar',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\cupertino_navigation_bar.py',
   'PYMODULE'),
  ('flet.core.cupertino_picker',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\cupertino_picker.py',
   'PYMODULE'),
  ('flet.core.cupertino_radio',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\cupertino_radio.py',
   'PYMODULE'),
  ('flet.core.cupertino_segmented_button',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\cupertino_segmented_button.py',
   'PYMODULE'),
  ('flet.core.cupertino_slider',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\cupertino_slider.py',
   'PYMODULE'),
  ('flet.core.cupertino_sliding_segmented_button',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\cupertino_sliding_segmented_button.py',
   'PYMODULE'),
  ('flet.core.cupertino_switch',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\cupertino_switch.py',
   'PYMODULE'),
  ('flet.core.cupertino_textfield',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\cupertino_textfield.py',
   'PYMODULE'),
  ('flet.core.cupertino_timer_picker',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\cupertino_timer_picker.py',
   'PYMODULE'),
  ('flet.core.datatable',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\datatable.py',
   'PYMODULE'),
  ('flet.core.date_picker',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\date_picker.py',
   'PYMODULE'),
  ('flet.core.dismissible',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\dismissible.py',
   'PYMODULE'),
  ('flet.core.divider',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\divider.py',
   'PYMODULE'),
  ('flet.core.drag_target',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\drag_target.py',
   'PYMODULE'),
  ('flet.core.draggable',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\draggable.py',
   'PYMODULE'),
  ('flet.core.dropdown',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\dropdown.py',
   'PYMODULE'),
  ('flet.core.dropdownm2',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\dropdownm2.py',
   'PYMODULE'),
  ('flet.core.elevated_button',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\elevated_button.py',
   'PYMODULE'),
  ('flet.core.embed_json_encoder',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\embed_json_encoder.py',
   'PYMODULE'),
  ('flet.core.event',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\event.py',
   'PYMODULE'),
  ('flet.core.event_handler',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\event_handler.py',
   'PYMODULE'),
  ('flet.core.exceptions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\exceptions.py',
   'PYMODULE'),
  ('flet.core.expansion_panel',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\expansion_panel.py',
   'PYMODULE'),
  ('flet.core.expansion_tile',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\expansion_tile.py',
   'PYMODULE'),
  ('flet.core.file_picker',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\file_picker.py',
   'PYMODULE'),
  ('flet.core.filled_button',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\filled_button.py',
   'PYMODULE'),
  ('flet.core.filled_tonal_button',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\filled_tonal_button.py',
   'PYMODULE'),
  ('flet.core.flashlight',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\flashlight.py',
   'PYMODULE'),
  ('flet.core.flet_app',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\flet_app.py',
   'PYMODULE'),
  ('flet.core.floating_action_button',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\floating_action_button.py',
   'PYMODULE'),
  ('flet.core.form_field_control',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\form_field_control.py',
   'PYMODULE'),
  ('flet.core.geolocator',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\geolocator.py',
   'PYMODULE'),
  ('flet.core.gesture_detector',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\gesture_detector.py',
   'PYMODULE'),
  ('flet.core.gradients',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\gradients.py',
   'PYMODULE'),
  ('flet.core.grid_view',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\grid_view.py',
   'PYMODULE'),
  ('flet.core.haptic_feedback',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\haptic_feedback.py',
   'PYMODULE'),
  ('flet.core.icon',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\icon.py',
   'PYMODULE'),
  ('flet.core.icon_button',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\icon_button.py',
   'PYMODULE'),
  ('flet.core.icons',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\icons.py',
   'PYMODULE'),
  ('flet.core.image',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\image.py',
   'PYMODULE'),
  ('flet.core.inline_span',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\inline_span.py',
   'PYMODULE'),
  ('flet.core.interactive_viewer',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\interactive_viewer.py',
   'PYMODULE'),
  ('flet.core.list_tile',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\list_tile.py',
   'PYMODULE'),
  ('flet.core.list_view',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\list_view.py',
   'PYMODULE'),
  ('flet.core.local_connection',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\local_connection.py',
   'PYMODULE'),
  ('flet.core.locks',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\locks.py',
   'PYMODULE'),
  ('flet.core.lottie',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\lottie.py',
   'PYMODULE'),
  ('flet.core.margin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\margin.py',
   'PYMODULE'),
  ('flet.core.markdown',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\markdown.py',
   'PYMODULE'),
  ('flet.core.menu_bar',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\menu_bar.py',
   'PYMODULE'),
  ('flet.core.menu_item_button',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\menu_item_button.py',
   'PYMODULE'),
  ('flet.core.navigation_bar',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\navigation_bar.py',
   'PYMODULE'),
  ('flet.core.navigation_drawer',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\navigation_drawer.py',
   'PYMODULE'),
  ('flet.core.navigation_rail',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\navigation_rail.py',
   'PYMODULE'),
  ('flet.core.outlined_button',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\outlined_button.py',
   'PYMODULE'),
  ('flet.core.padding',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\padding.py',
   'PYMODULE'),
  ('flet.core.page',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\page.py',
   'PYMODULE'),
  ('flet.core.pagelet',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\pagelet.py',
   'PYMODULE'),
  ('flet.core.painting',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\painting.py',
   'PYMODULE'),
  ('flet.core.permission_handler',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\permission_handler.py',
   'PYMODULE'),
  ('flet.core.placeholder',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\placeholder.py',
   'PYMODULE'),
  ('flet.core.popup_menu_button',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\popup_menu_button.py',
   'PYMODULE'),
  ('flet.core.progress_bar',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\progress_bar.py',
   'PYMODULE'),
  ('flet.core.progress_ring',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\progress_ring.py',
   'PYMODULE'),
  ('flet.core.protocol',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\protocol.py',
   'PYMODULE'),
  ('flet.core.pubsub', '-', 'PYMODULE'),
  ('flet.core.pubsub.pubsub_client',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\pubsub\\pubsub_client.py',
   'PYMODULE'),
  ('flet.core.pubsub.pubsub_hub',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\pubsub\\pubsub_hub.py',
   'PYMODULE'),
  ('flet.core.querystring',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\querystring.py',
   'PYMODULE'),
  ('flet.core.radio',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\radio.py',
   'PYMODULE'),
  ('flet.core.radio_group',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\radio_group.py',
   'PYMODULE'),
  ('flet.core.range_slider',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\range_slider.py',
   'PYMODULE'),
  ('flet.core.ref',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\ref.py',
   'PYMODULE'),
  ('flet.core.reorderable_draggable',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\reorderable_draggable.py',
   'PYMODULE'),
  ('flet.core.reorderable_list_view',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\reorderable_list_view.py',
   'PYMODULE'),
  ('flet.core.responsive_row',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\responsive_row.py',
   'PYMODULE'),
  ('flet.core.rive',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\rive.py',
   'PYMODULE'),
  ('flet.core.row',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\row.py',
   'PYMODULE'),
  ('flet.core.safe_area',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\safe_area.py',
   'PYMODULE'),
  ('flet.core.scrollable_control',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\scrollable_control.py',
   'PYMODULE'),
  ('flet.core.search_bar',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\search_bar.py',
   'PYMODULE'),
  ('flet.core.segmented_button',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\segmented_button.py',
   'PYMODULE'),
  ('flet.core.selection_area',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\selection_area.py',
   'PYMODULE'),
  ('flet.core.semantics',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\semantics.py',
   'PYMODULE'),
  ('flet.core.semantics_service',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\semantics_service.py',
   'PYMODULE'),
  ('flet.core.session_storage',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\session_storage.py',
   'PYMODULE'),
  ('flet.core.shader_mask',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\shader_mask.py',
   'PYMODULE'),
  ('flet.core.shake_detector',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\shake_detector.py',
   'PYMODULE'),
  ('flet.core.size',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\size.py',
   'PYMODULE'),
  ('flet.core.slider',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\slider.py',
   'PYMODULE'),
  ('flet.core.snack_bar',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\snack_bar.py',
   'PYMODULE'),
  ('flet.core.stack',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\stack.py',
   'PYMODULE'),
  ('flet.core.submenu_button',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\submenu_button.py',
   'PYMODULE'),
  ('flet.core.switch',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\switch.py',
   'PYMODULE'),
  ('flet.core.tabs',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\tabs.py',
   'PYMODULE'),
  ('flet.core.template_route',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\template_route.py',
   'PYMODULE'),
  ('flet.core.text',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\text.py',
   'PYMODULE'),
  ('flet.core.text_button',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\text_button.py',
   'PYMODULE'),
  ('flet.core.text_span',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\text_span.py',
   'PYMODULE'),
  ('flet.core.text_style',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\text_style.py',
   'PYMODULE'),
  ('flet.core.textfield',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\textfield.py',
   'PYMODULE'),
  ('flet.core.theme',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\theme.py',
   'PYMODULE'),
  ('flet.core.time_picker',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\time_picker.py',
   'PYMODULE'),
  ('flet.core.tooltip',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\tooltip.py',
   'PYMODULE'),
  ('flet.core.transform',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\transform.py',
   'PYMODULE'),
  ('flet.core.transparent_pointer',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\transparent_pointer.py',
   'PYMODULE'),
  ('flet.core.types',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\types.py',
   'PYMODULE'),
  ('flet.core.vertical_divider',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\vertical_divider.py',
   'PYMODULE'),
  ('flet.core.video',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\video.py',
   'PYMODULE'),
  ('flet.core.view',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\view.py',
   'PYMODULE'),
  ('flet.core.webview',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\webview.py',
   'PYMODULE'),
  ('flet.core.window_drag_area',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\core\\window_drag_area.py',
   'PYMODULE'),
  ('flet.flet_socket_server',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\flet_socket_server.py',
   'PYMODULE'),
  ('flet.pyodide_connection',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\pyodide_connection.py',
   'PYMODULE'),
  ('flet.utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\utils\\__init__.py',
   'PYMODULE'),
  ('flet.utils.browser',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\utils\\browser.py',
   'PYMODULE'),
  ('flet.utils.classproperty',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\utils\\classproperty.py',
   'PYMODULE'),
  ('flet.utils.deprecated',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\utils\\deprecated.py',
   'PYMODULE'),
  ('flet.utils.files',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\utils\\files.py',
   'PYMODULE'),
  ('flet.utils.hashing',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\utils\\hashing.py',
   'PYMODULE'),
  ('flet.utils.network',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\utils\\network.py',
   'PYMODULE'),
  ('flet.utils.once',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\utils\\once.py',
   'PYMODULE'),
  ('flet.utils.pip',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\utils\\pip.py',
   'PYMODULE'),
  ('flet.utils.platform_utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\utils\\platform_utils.py',
   'PYMODULE'),
  ('flet.utils.slugify',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\utils\\slugify.py',
   'PYMODULE'),
  ('flet.utils.strings',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\utils\\strings.py',
   'PYMODULE'),
  ('flet.utils.vector',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\utils\\vector.py',
   'PYMODULE'),
  ('flet.version',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet\\version.py',
   'PYMODULE'),
  ('flet_desktop',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet_desktop\\__init__.py',
   'PYMODULE'),
  ('flet_desktop.version',
   'D:\\Python\\Python39-32\\lib\\site-packages\\flet_desktop\\version.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\Python\\Python39-32\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\Python\\Python39-32\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\Python\\Python39-32\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\Python\\Python39-32\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\Python\\Python39-32\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\Python\\Python39-32\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\Python\\Python39-32\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\Python\\Python39-32\\lib\\gzip.py', 'PYMODULE'),
  ('h11',
   'D:\\Python\\Python39-32\\lib\\site-packages\\h11\\__init__.py',
   'PYMODULE'),
  ('h11._abnf',
   'D:\\Python\\Python39-32\\lib\\site-packages\\h11\\_abnf.py',
   'PYMODULE'),
  ('h11._connection',
   'D:\\Python\\Python39-32\\lib\\site-packages\\h11\\_connection.py',
   'PYMODULE'),
  ('h11._events',
   'D:\\Python\\Python39-32\\lib\\site-packages\\h11\\_events.py',
   'PYMODULE'),
  ('h11._headers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\h11\\_headers.py',
   'PYMODULE'),
  ('h11._readers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\h11\\_readers.py',
   'PYMODULE'),
  ('h11._receivebuffer',
   'D:\\Python\\Python39-32\\lib\\site-packages\\h11\\_receivebuffer.py',
   'PYMODULE'),
  ('h11._state',
   'D:\\Python\\Python39-32\\lib\\site-packages\\h11\\_state.py',
   'PYMODULE'),
  ('h11._util',
   'D:\\Python\\Python39-32\\lib\\site-packages\\h11\\_util.py',
   'PYMODULE'),
  ('h11._version',
   'D:\\Python\\Python39-32\\lib\\site-packages\\h11\\_version.py',
   'PYMODULE'),
  ('h11._writers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\h11\\_writers.py',
   'PYMODULE'),
  ('hashlib', 'D:\\Python\\Python39-32\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\Python\\Python39-32\\lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\Python\\Python39-32\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'D:\\Python\\Python39-32\\lib\\html\\entities.py',
   'PYMODULE'),
  ('http', 'D:\\Python\\Python39-32\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'D:\\Python\\Python39-32\\lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar',
   'D:\\Python\\Python39-32\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server', 'D:\\Python\\Python39-32\\lib\\http\\server.py', 'PYMODULE'),
  ('httpcore',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\__init__.py',
   'PYMODULE'),
  ('httpcore._api',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_api.py',
   'PYMODULE'),
  ('httpcore._async',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_async\\__init__.py',
   'PYMODULE'),
  ('httpcore._async.connection',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_async\\connection.py',
   'PYMODULE'),
  ('httpcore._async.connection_pool',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_async\\connection_pool.py',
   'PYMODULE'),
  ('httpcore._async.http11',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_async\\http11.py',
   'PYMODULE'),
  ('httpcore._async.http2',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_async\\http2.py',
   'PYMODULE'),
  ('httpcore._async.http_proxy',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_async\\http_proxy.py',
   'PYMODULE'),
  ('httpcore._async.interfaces',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_async\\interfaces.py',
   'PYMODULE'),
  ('httpcore._async.socks_proxy',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_async\\socks_proxy.py',
   'PYMODULE'),
  ('httpcore._backends',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_backends\\__init__.py',
   'PYMODULE'),
  ('httpcore._backends.anyio',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_backends\\anyio.py',
   'PYMODULE'),
  ('httpcore._backends.auto',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_backends\\auto.py',
   'PYMODULE'),
  ('httpcore._backends.base',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_backends\\base.py',
   'PYMODULE'),
  ('httpcore._backends.mock',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_backends\\mock.py',
   'PYMODULE'),
  ('httpcore._backends.sync',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_backends\\sync.py',
   'PYMODULE'),
  ('httpcore._backends.trio',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_backends\\trio.py',
   'PYMODULE'),
  ('httpcore._exceptions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_exceptions.py',
   'PYMODULE'),
  ('httpcore._models',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_models.py',
   'PYMODULE'),
  ('httpcore._ssl',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_ssl.py',
   'PYMODULE'),
  ('httpcore._sync',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_sync\\__init__.py',
   'PYMODULE'),
  ('httpcore._sync.connection',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_sync\\connection.py',
   'PYMODULE'),
  ('httpcore._sync.connection_pool',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_sync\\connection_pool.py',
   'PYMODULE'),
  ('httpcore._sync.http11',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_sync\\http11.py',
   'PYMODULE'),
  ('httpcore._sync.http2',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_sync\\http2.py',
   'PYMODULE'),
  ('httpcore._sync.http_proxy',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_sync\\http_proxy.py',
   'PYMODULE'),
  ('httpcore._sync.interfaces',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_sync\\interfaces.py',
   'PYMODULE'),
  ('httpcore._sync.socks_proxy',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_sync\\socks_proxy.py',
   'PYMODULE'),
  ('httpcore._synchronization',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_synchronization.py',
   'PYMODULE'),
  ('httpcore._trace',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_trace.py',
   'PYMODULE'),
  ('httpcore._utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpcore\\_utils.py',
   'PYMODULE'),
  ('httpx',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpx\\__init__.py',
   'PYMODULE'),
  ('httpx.__version__',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpx\\__version__.py',
   'PYMODULE'),
  ('httpx._api',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpx\\_api.py',
   'PYMODULE'),
  ('httpx._auth',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpx\\_auth.py',
   'PYMODULE'),
  ('httpx._client',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpx\\_client.py',
   'PYMODULE'),
  ('httpx._config',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpx\\_config.py',
   'PYMODULE'),
  ('httpx._content',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpx\\_content.py',
   'PYMODULE'),
  ('httpx._decoders',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpx\\_decoders.py',
   'PYMODULE'),
  ('httpx._exceptions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpx\\_exceptions.py',
   'PYMODULE'),
  ('httpx._main',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpx\\_main.py',
   'PYMODULE'),
  ('httpx._models',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpx\\_models.py',
   'PYMODULE'),
  ('httpx._multipart',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpx\\_multipart.py',
   'PYMODULE'),
  ('httpx._status_codes',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpx\\_status_codes.py',
   'PYMODULE'),
  ('httpx._transports',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpx\\_transports\\__init__.py',
   'PYMODULE'),
  ('httpx._transports.asgi',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpx\\_transports\\asgi.py',
   'PYMODULE'),
  ('httpx._transports.base',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpx\\_transports\\base.py',
   'PYMODULE'),
  ('httpx._transports.default',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpx\\_transports\\default.py',
   'PYMODULE'),
  ('httpx._transports.mock',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpx\\_transports\\mock.py',
   'PYMODULE'),
  ('httpx._transports.wsgi',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpx\\_transports\\wsgi.py',
   'PYMODULE'),
  ('httpx._types',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpx\\_types.py',
   'PYMODULE'),
  ('httpx._urlparse',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpx\\_urlparse.py',
   'PYMODULE'),
  ('httpx._urls',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpx\\_urls.py',
   'PYMODULE'),
  ('httpx._utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\httpx\\_utils.py',
   'PYMODULE'),
  ('idna',
   'D:\\Python\\Python39-32\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'D:\\Python\\Python39-32\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\Python\\Python39-32\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\Python\\Python39-32\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\Python\\Python39-32\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\Python\\Python39-32\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('imp', 'D:\\Python\\Python39-32\\lib\\imp.py', 'PYMODULE'),
  ('importlib',
   'D:\\Python\\Python39-32\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Python\\Python39-32\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Python\\Python39-32\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._common',
   'D:\\Python\\Python39-32\\lib\\importlib\\_common.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Python\\Python39-32\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\Python\\Python39-32\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Python\\Python39-32\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Python\\Python39-32\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\Python\\Python39-32\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib_metadata',
   'D:\\Python\\Python39-32\\lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'D:\\Python\\Python39-32\\lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'D:\\Python\\Python39-32\\lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'D:\\Python\\Python39-32\\lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'D:\\Python\\Python39-32\\lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'D:\\Python\\Python39-32\\lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'D:\\Python\\Python39-32\\lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata._typing',
   'D:\\Python\\Python39-32\\lib\\site-packages\\importlib_metadata\\_typing.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'D:\\Python\\Python39-32\\lib\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'D:\\Python\\Python39-32\\lib\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('inspect', 'D:\\Python\\Python39-32\\lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\Python\\Python39-32\\lib\\ipaddress.py', 'PYMODULE'),
  ('jiter',
   'D:\\Python\\Python39-32\\lib\\site-packages\\jiter\\__init__.py',
   'PYMODULE'),
  ('json', 'D:\\Python\\Python39-32\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'D:\\Python\\Python39-32\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\Python\\Python39-32\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Python\\Python39-32\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('keyboard',
   'D:\\Python\\Python39-32\\lib\\site-packages\\keyboard\\__init__.py',
   'PYMODULE'),
  ('keyboard._canonical_names',
   'D:\\Python\\Python39-32\\lib\\site-packages\\keyboard\\_canonical_names.py',
   'PYMODULE'),
  ('keyboard._darwinkeyboard',
   'D:\\Python\\Python39-32\\lib\\site-packages\\keyboard\\_darwinkeyboard.py',
   'PYMODULE'),
  ('keyboard._generic',
   'D:\\Python\\Python39-32\\lib\\site-packages\\keyboard\\_generic.py',
   'PYMODULE'),
  ('keyboard._keyboard_event',
   'D:\\Python\\Python39-32\\lib\\site-packages\\keyboard\\_keyboard_event.py',
   'PYMODULE'),
  ('keyboard._nixcommon',
   'D:\\Python\\Python39-32\\lib\\site-packages\\keyboard\\_nixcommon.py',
   'PYMODULE'),
  ('keyboard._nixkeyboard',
   'D:\\Python\\Python39-32\\lib\\site-packages\\keyboard\\_nixkeyboard.py',
   'PYMODULE'),
  ('keyboard._winkeyboard',
   'D:\\Python\\Python39-32\\lib\\site-packages\\keyboard\\_winkeyboard.py',
   'PYMODULE'),
  ('logging', 'D:\\Python\\Python39-32\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'D:\\Python\\Python39-32\\lib\\lzma.py', 'PYMODULE'),
  ('markdown_it',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\__init__.py',
   'PYMODULE'),
  ('markdown_it._compat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\_compat.py',
   'PYMODULE'),
  ('markdown_it._punycode',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\_punycode.py',
   'PYMODULE'),
  ('markdown_it.common',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\common\\__init__.py',
   'PYMODULE'),
  ('markdown_it.common.entities',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\common\\entities.py',
   'PYMODULE'),
  ('markdown_it.common.html_blocks',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\common\\html_blocks.py',
   'PYMODULE'),
  ('markdown_it.common.html_re',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\common\\html_re.py',
   'PYMODULE'),
  ('markdown_it.common.normalize_url',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\common\\normalize_url.py',
   'PYMODULE'),
  ('markdown_it.common.utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\common\\utils.py',
   'PYMODULE'),
  ('markdown_it.helpers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\helpers\\__init__.py',
   'PYMODULE'),
  ('markdown_it.helpers.parse_link_destination',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\helpers\\parse_link_destination.py',
   'PYMODULE'),
  ('markdown_it.helpers.parse_link_label',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\helpers\\parse_link_label.py',
   'PYMODULE'),
  ('markdown_it.helpers.parse_link_title',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\helpers\\parse_link_title.py',
   'PYMODULE'),
  ('markdown_it.main',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\main.py',
   'PYMODULE'),
  ('markdown_it.parser_block',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\parser_block.py',
   'PYMODULE'),
  ('markdown_it.parser_core',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\parser_core.py',
   'PYMODULE'),
  ('markdown_it.parser_inline',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\parser_inline.py',
   'PYMODULE'),
  ('markdown_it.presets',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\presets\\__init__.py',
   'PYMODULE'),
  ('markdown_it.presets.commonmark',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\presets\\commonmark.py',
   'PYMODULE'),
  ('markdown_it.presets.default',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\presets\\default.py',
   'PYMODULE'),
  ('markdown_it.presets.zero',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\presets\\zero.py',
   'PYMODULE'),
  ('markdown_it.renderer',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\renderer.py',
   'PYMODULE'),
  ('markdown_it.ruler',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\ruler.py',
   'PYMODULE'),
  ('markdown_it.rules_block',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_block\\__init__.py',
   'PYMODULE'),
  ('markdown_it.rules_block.blockquote',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_block\\blockquote.py',
   'PYMODULE'),
  ('markdown_it.rules_block.code',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_block\\code.py',
   'PYMODULE'),
  ('markdown_it.rules_block.fence',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_block\\fence.py',
   'PYMODULE'),
  ('markdown_it.rules_block.heading',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_block\\heading.py',
   'PYMODULE'),
  ('markdown_it.rules_block.hr',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_block\\hr.py',
   'PYMODULE'),
  ('markdown_it.rules_block.html_block',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_block\\html_block.py',
   'PYMODULE'),
  ('markdown_it.rules_block.lheading',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_block\\lheading.py',
   'PYMODULE'),
  ('markdown_it.rules_block.list',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_block\\list.py',
   'PYMODULE'),
  ('markdown_it.rules_block.paragraph',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_block\\paragraph.py',
   'PYMODULE'),
  ('markdown_it.rules_block.reference',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_block\\reference.py',
   'PYMODULE'),
  ('markdown_it.rules_block.state_block',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_block\\state_block.py',
   'PYMODULE'),
  ('markdown_it.rules_block.table',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_block\\table.py',
   'PYMODULE'),
  ('markdown_it.rules_core',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_core\\__init__.py',
   'PYMODULE'),
  ('markdown_it.rules_core.block',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_core\\block.py',
   'PYMODULE'),
  ('markdown_it.rules_core.inline',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_core\\inline.py',
   'PYMODULE'),
  ('markdown_it.rules_core.linkify',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_core\\linkify.py',
   'PYMODULE'),
  ('markdown_it.rules_core.normalize',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_core\\normalize.py',
   'PYMODULE'),
  ('markdown_it.rules_core.replacements',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_core\\replacements.py',
   'PYMODULE'),
  ('markdown_it.rules_core.smartquotes',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_core\\smartquotes.py',
   'PYMODULE'),
  ('markdown_it.rules_core.state_core',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_core\\state_core.py',
   'PYMODULE'),
  ('markdown_it.rules_core.text_join',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_core\\text_join.py',
   'PYMODULE'),
  ('markdown_it.rules_inline',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_inline\\__init__.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.autolink',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_inline\\autolink.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.backticks',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_inline\\backticks.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.balance_pairs',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_inline\\balance_pairs.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.emphasis',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_inline\\emphasis.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.entity',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_inline\\entity.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.escape',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_inline\\escape.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.fragments_join',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_inline\\fragments_join.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.html_inline',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_inline\\html_inline.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.image',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_inline\\image.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.link',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_inline\\link.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.linkify',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_inline\\linkify.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.newline',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_inline\\newline.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.state_inline',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_inline\\state_inline.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.strikethrough',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_inline\\strikethrough.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.text',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\rules_inline\\text.py',
   'PYMODULE'),
  ('markdown_it.token',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\token.py',
   'PYMODULE'),
  ('markdown_it.utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\markdown_it\\utils.py',
   'PYMODULE'),
  ('mdurl',
   'D:\\Python\\Python39-32\\lib\\site-packages\\mdurl\\__init__.py',
   'PYMODULE'),
  ('mdurl._decode',
   'D:\\Python\\Python39-32\\lib\\site-packages\\mdurl\\_decode.py',
   'PYMODULE'),
  ('mdurl._encode',
   'D:\\Python\\Python39-32\\lib\\site-packages\\mdurl\\_encode.py',
   'PYMODULE'),
  ('mdurl._format',
   'D:\\Python\\Python39-32\\lib\\site-packages\\mdurl\\_format.py',
   'PYMODULE'),
  ('mdurl._parse',
   'D:\\Python\\Python39-32\\lib\\site-packages\\mdurl\\_parse.py',
   'PYMODULE'),
  ('mdurl._url',
   'D:\\Python\\Python39-32\\lib\\site-packages\\mdurl\\_url.py',
   'PYMODULE'),
  ('mimetypes', 'D:\\Python\\Python39-32\\lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\Python\\Python39-32\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\Python\\Python39-32\\lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\Python\\Python39-32\\lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.array_api',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._indexing_functions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_indexing_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.compat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('oauthlib',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\__init__.py',
   'PYMODULE'),
  ('oauthlib.common',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\common.py',
   'PYMODULE'),
  ('oauthlib.oauth2',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\__init__.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\__init__.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.clients',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\clients\\__init__.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.clients.backend_application',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\clients\\backend_application.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.clients.base',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\clients\\base.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.clients.legacy_application',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\clients\\legacy_application.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.clients.mobile_application',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\clients\\mobile_application.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.clients.service_application',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\clients\\service_application.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.clients.web_application',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\clients\\web_application.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.endpoints',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\endpoints\\__init__.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.endpoints.authorization',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\endpoints\\authorization.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.endpoints.base',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\endpoints\\base.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.endpoints.introspect',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\endpoints\\introspect.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.endpoints.metadata',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\endpoints\\metadata.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.endpoints.pre_configured',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\endpoints\\pre_configured.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.endpoints.resource',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\endpoints\\resource.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.endpoints.revocation',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\endpoints\\revocation.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.endpoints.token',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\endpoints\\token.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.errors',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\errors.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.grant_types',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\grant_types\\__init__.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.grant_types.authorization_code',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\grant_types\\authorization_code.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.grant_types.base',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\grant_types\\base.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.grant_types.client_credentials',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\grant_types\\client_credentials.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.grant_types.implicit',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\grant_types\\implicit.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.grant_types.refresh_token',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\grant_types\\refresh_token.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.grant_types.resource_owner_password_credentials',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\grant_types\\resource_owner_password_credentials.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.parameters',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\parameters.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.request_validator',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\request_validator.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.tokens',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\tokens.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc6749.utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc6749\\utils.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc8628',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc8628\\__init__.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc8628.clients',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc8628\\clients\\__init__.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc8628.clients.device',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc8628\\clients\\device.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc8628.endpoints',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc8628\\endpoints\\__init__.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc8628.endpoints.device_authorization',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc8628\\endpoints\\device_authorization.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc8628.endpoints.pre_configured',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc8628\\endpoints\\pre_configured.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc8628.errors',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc8628\\errors.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc8628.grant_types',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc8628\\grant_types\\__init__.py',
   'PYMODULE'),
  ('oauthlib.oauth2.rfc8628.grant_types.device_code',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\oauth2\\rfc8628\\grant_types\\device_code.py',
   'PYMODULE'),
  ('oauthlib.openid',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\openid\\__init__.py',
   'PYMODULE'),
  ('oauthlib.openid.connect',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\openid\\connect\\__init__.py',
   'PYMODULE'),
  ('oauthlib.openid.connect.core',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\openid\\connect\\core\\__init__.py',
   'PYMODULE'),
  ('oauthlib.openid.connect.core.endpoints',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\openid\\connect\\core\\endpoints\\__init__.py',
   'PYMODULE'),
  ('oauthlib.openid.connect.core.endpoints.pre_configured',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\openid\\connect\\core\\endpoints\\pre_configured.py',
   'PYMODULE'),
  ('oauthlib.openid.connect.core.endpoints.userinfo',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\openid\\connect\\core\\endpoints\\userinfo.py',
   'PYMODULE'),
  ('oauthlib.openid.connect.core.grant_types',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\openid\\connect\\core\\grant_types\\__init__.py',
   'PYMODULE'),
  ('oauthlib.openid.connect.core.grant_types.authorization_code',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\openid\\connect\\core\\grant_types\\authorization_code.py',
   'PYMODULE'),
  ('oauthlib.openid.connect.core.grant_types.base',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\openid\\connect\\core\\grant_types\\base.py',
   'PYMODULE'),
  ('oauthlib.openid.connect.core.grant_types.dispatchers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\openid\\connect\\core\\grant_types\\dispatchers.py',
   'PYMODULE'),
  ('oauthlib.openid.connect.core.grant_types.hybrid',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\openid\\connect\\core\\grant_types\\hybrid.py',
   'PYMODULE'),
  ('oauthlib.openid.connect.core.grant_types.implicit',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\openid\\connect\\core\\grant_types\\implicit.py',
   'PYMODULE'),
  ('oauthlib.openid.connect.core.grant_types.refresh_token',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\openid\\connect\\core\\grant_types\\refresh_token.py',
   'PYMODULE'),
  ('oauthlib.openid.connect.core.request_validator',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\openid\\connect\\core\\request_validator.py',
   'PYMODULE'),
  ('oauthlib.openid.connect.core.tokens',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\openid\\connect\\core\\tokens.py',
   'PYMODULE'),
  ('oauthlib.signals',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\signals.py',
   'PYMODULE'),
  ('oauthlib.uri_validate',
   'D:\\Python\\Python39-32\\lib\\site-packages\\oauthlib\\uri_validate.py',
   'PYMODULE'),
  ('opcode', 'D:\\Python\\Python39-32\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'D:\\Python\\Python39-32\\lib\\optparse.py', 'PYMODULE'),
  ('outcome',
   'D:\\Python\\Python39-32\\lib\\site-packages\\outcome\\__init__.py',
   'PYMODULE'),
  ('outcome._impl',
   'D:\\Python\\Python39-32\\lib\\site-packages\\outcome\\_impl.py',
   'PYMODULE'),
  ('outcome._util',
   'D:\\Python\\Python39-32\\lib\\site-packages\\outcome\\_util.py',
   'PYMODULE'),
  ('outcome._version',
   'D:\\Python\\Python39-32\\lib\\site-packages\\outcome\\_version.py',
   'PYMODULE'),
  ('packaging',
   'D:\\Python\\Python39-32\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\Python\\Python39-32\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\Python\\Python39-32\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\Python\\Python39-32\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\Python\\Python39-32\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\Python\\Python39-32\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\Python\\Python39-32\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\Python\\Python39-32\\lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\Python\\Python39-32\\lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'D:\\Python\\Python39-32\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\Python\\Python39-32\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\Python\\Python39-32\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\Python\\Python39-32\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib', 'D:\\Python\\Python39-32\\lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'D:\\Python\\Python39-32\\lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\Python\\Python39-32\\lib\\pickle.py', 'PYMODULE'),
  ('pkg_resources',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._compat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._typing',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_typing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'D:\\Python\\Python39-32\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\Python\\Python39-32\\lib\\platform.py', 'PYMODULE'),
  ('plistlib', 'D:\\Python\\Python39-32\\lib\\plistlib.py', 'PYMODULE'),
  ('pprint', 'D:\\Python\\Python39-32\\lib\\pprint.py', 'PYMODULE'),
  ('psutil',
   'D:\\Python\\Python39-32\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\Python\\Python39-32\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\Python\\Python39-32\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('pty', 'D:\\Python\\Python39-32\\lib\\pty.py', 'PYMODULE'),
  ('py_compile', 'D:\\Python\\Python39-32\\lib\\py_compile.py', 'PYMODULE'),
  ('pycparser',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydantic',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\__init__.py',
   'PYMODULE'),
  ('pydantic._internal',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\__init__.py',
   'PYMODULE'),
  ('pydantic._internal._config',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_config.py',
   'PYMODULE'),
  ('pydantic._internal._core_metadata',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_core_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._core_utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_core_utils.py',
   'PYMODULE'),
  ('pydantic._internal._dataclasses',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_dataclasses.py',
   'PYMODULE'),
  ('pydantic._internal._decorators',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_decorators.py',
   'PYMODULE'),
  ('pydantic._internal._decorators_v1',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_decorators_v1.py',
   'PYMODULE'),
  ('pydantic._internal._discriminated_union',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_discriminated_union.py',
   'PYMODULE'),
  ('pydantic._internal._docs_extraction',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_docs_extraction.py',
   'PYMODULE'),
  ('pydantic._internal._fields',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_fields.py',
   'PYMODULE'),
  ('pydantic._internal._forward_ref',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_forward_ref.py',
   'PYMODULE'),
  ('pydantic._internal._generate_schema',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_generate_schema.py',
   'PYMODULE'),
  ('pydantic._internal._generics',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_generics.py',
   'PYMODULE'),
  ('pydantic._internal._git',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_git.py',
   'PYMODULE'),
  ('pydantic._internal._import_utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_import_utils.py',
   'PYMODULE'),
  ('pydantic._internal._internal_dataclass',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_internal_dataclass.py',
   'PYMODULE'),
  ('pydantic._internal._known_annotated_metadata',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_known_annotated_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._mock_val_ser',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_mock_val_ser.py',
   'PYMODULE'),
  ('pydantic._internal._model_construction',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_model_construction.py',
   'PYMODULE'),
  ('pydantic._internal._namespace_utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_namespace_utils.py',
   'PYMODULE'),
  ('pydantic._internal._repr',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_repr.py',
   'PYMODULE'),
  ('pydantic._internal._schema_gather',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_schema_gather.py',
   'PYMODULE'),
  ('pydantic._internal._schema_generation_shared',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_schema_generation_shared.py',
   'PYMODULE'),
  ('pydantic._internal._serializers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_serializers.py',
   'PYMODULE'),
  ('pydantic._internal._signature',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_signature.py',
   'PYMODULE'),
  ('pydantic._internal._typing_extra',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_typing_extra.py',
   'PYMODULE'),
  ('pydantic._internal._utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_utils.py',
   'PYMODULE'),
  ('pydantic._internal._validate_call',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_validate_call.py',
   'PYMODULE'),
  ('pydantic._internal._validators',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_internal\\_validators.py',
   'PYMODULE'),
  ('pydantic._migration',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\_migration.py',
   'PYMODULE'),
  ('pydantic.alias_generators',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\alias_generators.py',
   'PYMODULE'),
  ('pydantic.aliases',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\aliases.py',
   'PYMODULE'),
  ('pydantic.annotated_handlers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\annotated_handlers.py',
   'PYMODULE'),
  ('pydantic.class_validators',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\class_validators.py',
   'PYMODULE'),
  ('pydantic.color',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\color.py',
   'PYMODULE'),
  ('pydantic.config',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\config.py',
   'PYMODULE'),
  ('pydantic.dataclasses',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\dataclasses.py',
   'PYMODULE'),
  ('pydantic.datetime_parse',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\datetime_parse.py',
   'PYMODULE'),
  ('pydantic.decorator',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\decorator.py',
   'PYMODULE'),
  ('pydantic.deprecated',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\deprecated\\__init__.py',
   'PYMODULE'),
  ('pydantic.deprecated.class_validators',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\deprecated\\class_validators.py',
   'PYMODULE'),
  ('pydantic.deprecated.config',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\deprecated\\config.py',
   'PYMODULE'),
  ('pydantic.deprecated.copy_internals',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\deprecated\\copy_internals.py',
   'PYMODULE'),
  ('pydantic.deprecated.decorator',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\deprecated\\decorator.py',
   'PYMODULE'),
  ('pydantic.deprecated.json',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\deprecated\\json.py',
   'PYMODULE'),
  ('pydantic.deprecated.parse',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\deprecated\\parse.py',
   'PYMODULE'),
  ('pydantic.deprecated.tools',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\deprecated\\tools.py',
   'PYMODULE'),
  ('pydantic.env_settings',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\env_settings.py',
   'PYMODULE'),
  ('pydantic.error_wrappers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.errors',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\errors.py',
   'PYMODULE'),
  ('pydantic.experimental',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\experimental\\__init__.py',
   'PYMODULE'),
  ('pydantic.experimental.arguments_schema',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\experimental\\arguments_schema.py',
   'PYMODULE'),
  ('pydantic.experimental.pipeline',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\experimental\\pipeline.py',
   'PYMODULE'),
  ('pydantic.fields',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\fields.py',
   'PYMODULE'),
  ('pydantic.functional_serializers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\functional_serializers.py',
   'PYMODULE'),
  ('pydantic.functional_validators',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\functional_validators.py',
   'PYMODULE'),
  ('pydantic.generics',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\generics.py',
   'PYMODULE'),
  ('pydantic.json',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\json.py',
   'PYMODULE'),
  ('pydantic.json_schema',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\json_schema.py',
   'PYMODULE'),
  ('pydantic.main',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\main.py',
   'PYMODULE'),
  ('pydantic.mypy',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\mypy.py',
   'PYMODULE'),
  ('pydantic.networks',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\networks.py',
   'PYMODULE'),
  ('pydantic.parse',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\parse.py',
   'PYMODULE'),
  ('pydantic.plugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\plugin\\__init__.py',
   'PYMODULE'),
  ('pydantic.plugin._loader',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\plugin\\_loader.py',
   'PYMODULE'),
  ('pydantic.plugin._schema_validator',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\plugin\\_schema_validator.py',
   'PYMODULE'),
  ('pydantic.root_model',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\root_model.py',
   'PYMODULE'),
  ('pydantic.schema',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\schema.py',
   'PYMODULE'),
  ('pydantic.tools',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\tools.py',
   'PYMODULE'),
  ('pydantic.type_adapter',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\type_adapter.py',
   'PYMODULE'),
  ('pydantic.types',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\types.py',
   'PYMODULE'),
  ('pydantic.typing',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\typing.py',
   'PYMODULE'),
  ('pydantic.utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\utils.py',
   'PYMODULE'),
  ('pydantic.v1',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\__init__.py',
   'PYMODULE'),
  ('pydantic.v1._hypothesis_plugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\_hypothesis_plugin.py',
   'PYMODULE'),
  ('pydantic.v1.annotated_types',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\annotated_types.py',
   'PYMODULE'),
  ('pydantic.v1.class_validators',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\class_validators.py',
   'PYMODULE'),
  ('pydantic.v1.color',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\color.py',
   'PYMODULE'),
  ('pydantic.v1.config',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\config.py',
   'PYMODULE'),
  ('pydantic.v1.dataclasses',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\dataclasses.py',
   'PYMODULE'),
  ('pydantic.v1.datetime_parse',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\datetime_parse.py',
   'PYMODULE'),
  ('pydantic.v1.decorator',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\decorator.py',
   'PYMODULE'),
  ('pydantic.v1.env_settings',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\env_settings.py',
   'PYMODULE'),
  ('pydantic.v1.error_wrappers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.v1.errors',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\errors.py',
   'PYMODULE'),
  ('pydantic.v1.fields',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\fields.py',
   'PYMODULE'),
  ('pydantic.v1.generics',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\generics.py',
   'PYMODULE'),
  ('pydantic.v1.json',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\json.py',
   'PYMODULE'),
  ('pydantic.v1.main',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\main.py',
   'PYMODULE'),
  ('pydantic.v1.mypy',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\mypy.py',
   'PYMODULE'),
  ('pydantic.v1.networks',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\networks.py',
   'PYMODULE'),
  ('pydantic.v1.parse',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\parse.py',
   'PYMODULE'),
  ('pydantic.v1.schema',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\schema.py',
   'PYMODULE'),
  ('pydantic.v1.tools',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\tools.py',
   'PYMODULE'),
  ('pydantic.v1.types',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\types.py',
   'PYMODULE'),
  ('pydantic.v1.typing',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\typing.py',
   'PYMODULE'),
  ('pydantic.v1.utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\utils.py',
   'PYMODULE'),
  ('pydantic.v1.validators',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\validators.py',
   'PYMODULE'),
  ('pydantic.v1.version',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\v1\\version.py',
   'PYMODULE'),
  ('pydantic.validate_call_decorator',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\validate_call_decorator.py',
   'PYMODULE'),
  ('pydantic.validators',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\validators.py',
   'PYMODULE'),
  ('pydantic.version',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\version.py',
   'PYMODULE'),
  ('pydantic.warnings',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic\\warnings.py',
   'PYMODULE'),
  ('pydantic_core',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic_core\\__init__.py',
   'PYMODULE'),
  ('pydantic_core.core_schema',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pydantic_core\\core_schema.py',
   'PYMODULE'),
  ('pydoc', 'D:\\Python\\Python39-32\\lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'D:\\Python\\Python39-32\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\Python\\Python39-32\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pygments',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\__init__.py',
   'PYMODULE'),
  ('pygments.console',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\console.py',
   'PYMODULE'),
  ('pygments.filter',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\filter.py',
   'PYMODULE'),
  ('pygments.filters',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\filters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatter',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\formatter.py',
   'PYMODULE'),
  ('pygments.formatters',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\formatters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatters._mapping',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\formatters\\_mapping.py',
   'PYMODULE'),
  ('pygments.formatters.bbcode',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\formatters\\bbcode.py',
   'PYMODULE'),
  ('pygments.formatters.groff',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\formatters\\groff.py',
   'PYMODULE'),
  ('pygments.formatters.html',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\formatters\\html.py',
   'PYMODULE'),
  ('pygments.formatters.img',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\formatters\\img.py',
   'PYMODULE'),
  ('pygments.formatters.irc',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\formatters\\irc.py',
   'PYMODULE'),
  ('pygments.formatters.latex',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\formatters\\latex.py',
   'PYMODULE'),
  ('pygments.formatters.other',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\formatters\\other.py',
   'PYMODULE'),
  ('pygments.formatters.pangomarkup',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\formatters\\pangomarkup.py',
   'PYMODULE'),
  ('pygments.formatters.rtf',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\formatters\\rtf.py',
   'PYMODULE'),
  ('pygments.formatters.svg',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\formatters\\svg.py',
   'PYMODULE'),
  ('pygments.formatters.terminal',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\formatters\\terminal.py',
   'PYMODULE'),
  ('pygments.formatters.terminal256',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\formatters\\terminal256.py',
   'PYMODULE'),
  ('pygments.lexer',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexer.py',
   'PYMODULE'),
  ('pygments.lexers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\__init__.py',
   'PYMODULE'),
  ('pygments.lexers._ada_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_ada_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._asy_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_asy_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cl_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_cl_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cocoa_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_cocoa_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._csound_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_csound_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._css_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_css_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._googlesql_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_googlesql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._julia_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_julia_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lasso_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_lasso_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lilypond_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_lilypond_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lua_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_lua_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._luau_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_luau_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mapping',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_mapping.py',
   'PYMODULE'),
  ('pygments.lexers._mql_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_mql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mysql_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_mysql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._openedge_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_openedge_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._php_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_php_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._postgres_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_postgres_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._qlik_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_qlik_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scheme_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_scheme_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scilab_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_scilab_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._sourcemod_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_sourcemod_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._sql_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_sql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stan_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_stan_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stata_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_stata_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._tsql_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_tsql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._usd_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_usd_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vbscript_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_vbscript_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vim_builtins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\_vim_builtins.py',
   'PYMODULE'),
  ('pygments.lexers.actionscript',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\actionscript.py',
   'PYMODULE'),
  ('pygments.lexers.ada',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\ada.py',
   'PYMODULE'),
  ('pygments.lexers.agile',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\agile.py',
   'PYMODULE'),
  ('pygments.lexers.algebra',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\algebra.py',
   'PYMODULE'),
  ('pygments.lexers.ambient',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\ambient.py',
   'PYMODULE'),
  ('pygments.lexers.amdgpu',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\amdgpu.py',
   'PYMODULE'),
  ('pygments.lexers.ampl',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\ampl.py',
   'PYMODULE'),
  ('pygments.lexers.apdlexer',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\apdlexer.py',
   'PYMODULE'),
  ('pygments.lexers.apl',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\apl.py',
   'PYMODULE'),
  ('pygments.lexers.archetype',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\archetype.py',
   'PYMODULE'),
  ('pygments.lexers.arrow',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\arrow.py',
   'PYMODULE'),
  ('pygments.lexers.arturo',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\arturo.py',
   'PYMODULE'),
  ('pygments.lexers.asc',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\asc.py',
   'PYMODULE'),
  ('pygments.lexers.asm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\asm.py',
   'PYMODULE'),
  ('pygments.lexers.asn1',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\asn1.py',
   'PYMODULE'),
  ('pygments.lexers.automation',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\automation.py',
   'PYMODULE'),
  ('pygments.lexers.bare',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\bare.py',
   'PYMODULE'),
  ('pygments.lexers.basic',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\basic.py',
   'PYMODULE'),
  ('pygments.lexers.bdd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\bdd.py',
   'PYMODULE'),
  ('pygments.lexers.berry',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\berry.py',
   'PYMODULE'),
  ('pygments.lexers.bibtex',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\bibtex.py',
   'PYMODULE'),
  ('pygments.lexers.blueprint',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\blueprint.py',
   'PYMODULE'),
  ('pygments.lexers.boa',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\boa.py',
   'PYMODULE'),
  ('pygments.lexers.bqn',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\bqn.py',
   'PYMODULE'),
  ('pygments.lexers.business',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\business.py',
   'PYMODULE'),
  ('pygments.lexers.c_cpp',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\c_cpp.py',
   'PYMODULE'),
  ('pygments.lexers.c_like',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\c_like.py',
   'PYMODULE'),
  ('pygments.lexers.capnproto',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\capnproto.py',
   'PYMODULE'),
  ('pygments.lexers.carbon',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\carbon.py',
   'PYMODULE'),
  ('pygments.lexers.cddl',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\cddl.py',
   'PYMODULE'),
  ('pygments.lexers.chapel',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\chapel.py',
   'PYMODULE'),
  ('pygments.lexers.clean',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\clean.py',
   'PYMODULE'),
  ('pygments.lexers.codeql',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\codeql.py',
   'PYMODULE'),
  ('pygments.lexers.comal',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\comal.py',
   'PYMODULE'),
  ('pygments.lexers.compiled',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\compiled.py',
   'PYMODULE'),
  ('pygments.lexers.configs',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\configs.py',
   'PYMODULE'),
  ('pygments.lexers.console',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\console.py',
   'PYMODULE'),
  ('pygments.lexers.cplint',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\cplint.py',
   'PYMODULE'),
  ('pygments.lexers.crystal',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\crystal.py',
   'PYMODULE'),
  ('pygments.lexers.csound',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\csound.py',
   'PYMODULE'),
  ('pygments.lexers.css',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\css.py',
   'PYMODULE'),
  ('pygments.lexers.d',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\d.py',
   'PYMODULE'),
  ('pygments.lexers.dalvik',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\dalvik.py',
   'PYMODULE'),
  ('pygments.lexers.data',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\data.py',
   'PYMODULE'),
  ('pygments.lexers.dax',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\dax.py',
   'PYMODULE'),
  ('pygments.lexers.devicetree',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\devicetree.py',
   'PYMODULE'),
  ('pygments.lexers.diff',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\diff.py',
   'PYMODULE'),
  ('pygments.lexers.dns',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\dns.py',
   'PYMODULE'),
  ('pygments.lexers.dotnet',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\dotnet.py',
   'PYMODULE'),
  ('pygments.lexers.dsls',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\dsls.py',
   'PYMODULE'),
  ('pygments.lexers.dylan',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\dylan.py',
   'PYMODULE'),
  ('pygments.lexers.ecl',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\ecl.py',
   'PYMODULE'),
  ('pygments.lexers.eiffel',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\eiffel.py',
   'PYMODULE'),
  ('pygments.lexers.elm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\elm.py',
   'PYMODULE'),
  ('pygments.lexers.elpi',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\elpi.py',
   'PYMODULE'),
  ('pygments.lexers.email',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\email.py',
   'PYMODULE'),
  ('pygments.lexers.erlang',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\erlang.py',
   'PYMODULE'),
  ('pygments.lexers.esoteric',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\esoteric.py',
   'PYMODULE'),
  ('pygments.lexers.ezhil',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\ezhil.py',
   'PYMODULE'),
  ('pygments.lexers.factor',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\factor.py',
   'PYMODULE'),
  ('pygments.lexers.fantom',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\fantom.py',
   'PYMODULE'),
  ('pygments.lexers.felix',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\felix.py',
   'PYMODULE'),
  ('pygments.lexers.fift',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\fift.py',
   'PYMODULE'),
  ('pygments.lexers.floscript',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\floscript.py',
   'PYMODULE'),
  ('pygments.lexers.forth',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\forth.py',
   'PYMODULE'),
  ('pygments.lexers.fortran',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\fortran.py',
   'PYMODULE'),
  ('pygments.lexers.foxpro',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\foxpro.py',
   'PYMODULE'),
  ('pygments.lexers.freefem',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\freefem.py',
   'PYMODULE'),
  ('pygments.lexers.func',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\func.py',
   'PYMODULE'),
  ('pygments.lexers.functional',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\functional.py',
   'PYMODULE'),
  ('pygments.lexers.futhark',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\futhark.py',
   'PYMODULE'),
  ('pygments.lexers.gcodelexer',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\gcodelexer.py',
   'PYMODULE'),
  ('pygments.lexers.gdscript',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\gdscript.py',
   'PYMODULE'),
  ('pygments.lexers.gleam',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\gleam.py',
   'PYMODULE'),
  ('pygments.lexers.go',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\go.py',
   'PYMODULE'),
  ('pygments.lexers.grammar_notation',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\grammar_notation.py',
   'PYMODULE'),
  ('pygments.lexers.graph',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\graph.py',
   'PYMODULE'),
  ('pygments.lexers.graphics',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\graphics.py',
   'PYMODULE'),
  ('pygments.lexers.graphql',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\graphql.py',
   'PYMODULE'),
  ('pygments.lexers.graphviz',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\graphviz.py',
   'PYMODULE'),
  ('pygments.lexers.gsql',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\gsql.py',
   'PYMODULE'),
  ('pygments.lexers.hare',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\hare.py',
   'PYMODULE'),
  ('pygments.lexers.haskell',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\haskell.py',
   'PYMODULE'),
  ('pygments.lexers.haxe',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\haxe.py',
   'PYMODULE'),
  ('pygments.lexers.hdl',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\hdl.py',
   'PYMODULE'),
  ('pygments.lexers.hexdump',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\hexdump.py',
   'PYMODULE'),
  ('pygments.lexers.html',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\html.py',
   'PYMODULE'),
  ('pygments.lexers.idl',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\idl.py',
   'PYMODULE'),
  ('pygments.lexers.igor',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\igor.py',
   'PYMODULE'),
  ('pygments.lexers.inferno',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\inferno.py',
   'PYMODULE'),
  ('pygments.lexers.installers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\installers.py',
   'PYMODULE'),
  ('pygments.lexers.int_fiction',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\int_fiction.py',
   'PYMODULE'),
  ('pygments.lexers.iolang',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\iolang.py',
   'PYMODULE'),
  ('pygments.lexers.j',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\j.py',
   'PYMODULE'),
  ('pygments.lexers.javascript',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\javascript.py',
   'PYMODULE'),
  ('pygments.lexers.jmespath',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\jmespath.py',
   'PYMODULE'),
  ('pygments.lexers.jslt',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\jslt.py',
   'PYMODULE'),
  ('pygments.lexers.json5',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\json5.py',
   'PYMODULE'),
  ('pygments.lexers.jsonnet',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\jsonnet.py',
   'PYMODULE'),
  ('pygments.lexers.jsx',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\jsx.py',
   'PYMODULE'),
  ('pygments.lexers.julia',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\julia.py',
   'PYMODULE'),
  ('pygments.lexers.jvm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\jvm.py',
   'PYMODULE'),
  ('pygments.lexers.kuin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\kuin.py',
   'PYMODULE'),
  ('pygments.lexers.kusto',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\kusto.py',
   'PYMODULE'),
  ('pygments.lexers.ldap',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\ldap.py',
   'PYMODULE'),
  ('pygments.lexers.lean',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\lean.py',
   'PYMODULE'),
  ('pygments.lexers.lilypond',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\lilypond.py',
   'PYMODULE'),
  ('pygments.lexers.lisp',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\lisp.py',
   'PYMODULE'),
  ('pygments.lexers.macaulay2',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\macaulay2.py',
   'PYMODULE'),
  ('pygments.lexers.make',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\make.py',
   'PYMODULE'),
  ('pygments.lexers.maple',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\maple.py',
   'PYMODULE'),
  ('pygments.lexers.markup',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\markup.py',
   'PYMODULE'),
  ('pygments.lexers.math',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\math.py',
   'PYMODULE'),
  ('pygments.lexers.matlab',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\matlab.py',
   'PYMODULE'),
  ('pygments.lexers.maxima',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\maxima.py',
   'PYMODULE'),
  ('pygments.lexers.meson',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\meson.py',
   'PYMODULE'),
  ('pygments.lexers.mime',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\mime.py',
   'PYMODULE'),
  ('pygments.lexers.minecraft',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\minecraft.py',
   'PYMODULE'),
  ('pygments.lexers.mips',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\mips.py',
   'PYMODULE'),
  ('pygments.lexers.ml',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\ml.py',
   'PYMODULE'),
  ('pygments.lexers.modeling',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\modeling.py',
   'PYMODULE'),
  ('pygments.lexers.modula2',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\modula2.py',
   'PYMODULE'),
  ('pygments.lexers.mojo',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\mojo.py',
   'PYMODULE'),
  ('pygments.lexers.monte',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\monte.py',
   'PYMODULE'),
  ('pygments.lexers.mosel',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\mosel.py',
   'PYMODULE'),
  ('pygments.lexers.ncl',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\ncl.py',
   'PYMODULE'),
  ('pygments.lexers.nimrod',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\nimrod.py',
   'PYMODULE'),
  ('pygments.lexers.nit',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\nit.py',
   'PYMODULE'),
  ('pygments.lexers.nix',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\nix.py',
   'PYMODULE'),
  ('pygments.lexers.numbair',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\numbair.py',
   'PYMODULE'),
  ('pygments.lexers.oberon',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\oberon.py',
   'PYMODULE'),
  ('pygments.lexers.objective',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\objective.py',
   'PYMODULE'),
  ('pygments.lexers.ooc',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\ooc.py',
   'PYMODULE'),
  ('pygments.lexers.openscad',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\openscad.py',
   'PYMODULE'),
  ('pygments.lexers.other',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\other.py',
   'PYMODULE'),
  ('pygments.lexers.parasail',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\parasail.py',
   'PYMODULE'),
  ('pygments.lexers.parsers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\parsers.py',
   'PYMODULE'),
  ('pygments.lexers.pascal',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\pascal.py',
   'PYMODULE'),
  ('pygments.lexers.pawn',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\pawn.py',
   'PYMODULE'),
  ('pygments.lexers.pddl',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\pddl.py',
   'PYMODULE'),
  ('pygments.lexers.perl',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\perl.py',
   'PYMODULE'),
  ('pygments.lexers.phix',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\phix.py',
   'PYMODULE'),
  ('pygments.lexers.php',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\php.py',
   'PYMODULE'),
  ('pygments.lexers.pointless',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\pointless.py',
   'PYMODULE'),
  ('pygments.lexers.pony',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\pony.py',
   'PYMODULE'),
  ('pygments.lexers.praat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\praat.py',
   'PYMODULE'),
  ('pygments.lexers.procfile',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\procfile.py',
   'PYMODULE'),
  ('pygments.lexers.prolog',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\prolog.py',
   'PYMODULE'),
  ('pygments.lexers.promql',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\promql.py',
   'PYMODULE'),
  ('pygments.lexers.prql',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\prql.py',
   'PYMODULE'),
  ('pygments.lexers.ptx',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\ptx.py',
   'PYMODULE'),
  ('pygments.lexers.python',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\python.py',
   'PYMODULE'),
  ('pygments.lexers.q',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\q.py',
   'PYMODULE'),
  ('pygments.lexers.qlik',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\qlik.py',
   'PYMODULE'),
  ('pygments.lexers.qvt',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\qvt.py',
   'PYMODULE'),
  ('pygments.lexers.r',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\r.py',
   'PYMODULE'),
  ('pygments.lexers.rdf',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\rdf.py',
   'PYMODULE'),
  ('pygments.lexers.rebol',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\rebol.py',
   'PYMODULE'),
  ('pygments.lexers.rego',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\rego.py',
   'PYMODULE'),
  ('pygments.lexers.resource',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\resource.py',
   'PYMODULE'),
  ('pygments.lexers.ride',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\ride.py',
   'PYMODULE'),
  ('pygments.lexers.rita',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\rita.py',
   'PYMODULE'),
  ('pygments.lexers.rnc',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\rnc.py',
   'PYMODULE'),
  ('pygments.lexers.roboconf',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\roboconf.py',
   'PYMODULE'),
  ('pygments.lexers.robotframework',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\robotframework.py',
   'PYMODULE'),
  ('pygments.lexers.ruby',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\ruby.py',
   'PYMODULE'),
  ('pygments.lexers.rust',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\rust.py',
   'PYMODULE'),
  ('pygments.lexers.sas',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\sas.py',
   'PYMODULE'),
  ('pygments.lexers.savi',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\savi.py',
   'PYMODULE'),
  ('pygments.lexers.scdoc',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\scdoc.py',
   'PYMODULE'),
  ('pygments.lexers.scripting',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\scripting.py',
   'PYMODULE'),
  ('pygments.lexers.sgf',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\sgf.py',
   'PYMODULE'),
  ('pygments.lexers.shell',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\shell.py',
   'PYMODULE'),
  ('pygments.lexers.sieve',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\sieve.py',
   'PYMODULE'),
  ('pygments.lexers.slash',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\slash.py',
   'PYMODULE'),
  ('pygments.lexers.smalltalk',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\smalltalk.py',
   'PYMODULE'),
  ('pygments.lexers.smithy',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\smithy.py',
   'PYMODULE'),
  ('pygments.lexers.smv',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\smv.py',
   'PYMODULE'),
  ('pygments.lexers.snobol',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\snobol.py',
   'PYMODULE'),
  ('pygments.lexers.solidity',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\solidity.py',
   'PYMODULE'),
  ('pygments.lexers.soong',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\soong.py',
   'PYMODULE'),
  ('pygments.lexers.sophia',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\sophia.py',
   'PYMODULE'),
  ('pygments.lexers.special',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\special.py',
   'PYMODULE'),
  ('pygments.lexers.spice',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\spice.py',
   'PYMODULE'),
  ('pygments.lexers.sql',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\sql.py',
   'PYMODULE'),
  ('pygments.lexers.srcinfo',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\srcinfo.py',
   'PYMODULE'),
  ('pygments.lexers.stata',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\stata.py',
   'PYMODULE'),
  ('pygments.lexers.supercollider',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\supercollider.py',
   'PYMODULE'),
  ('pygments.lexers.tablegen',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\tablegen.py',
   'PYMODULE'),
  ('pygments.lexers.tact',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\tact.py',
   'PYMODULE'),
  ('pygments.lexers.tal',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\tal.py',
   'PYMODULE'),
  ('pygments.lexers.tcl',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\tcl.py',
   'PYMODULE'),
  ('pygments.lexers.teal',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\teal.py',
   'PYMODULE'),
  ('pygments.lexers.templates',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\templates.py',
   'PYMODULE'),
  ('pygments.lexers.teraterm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\teraterm.py',
   'PYMODULE'),
  ('pygments.lexers.testing',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\testing.py',
   'PYMODULE'),
  ('pygments.lexers.text',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\text.py',
   'PYMODULE'),
  ('pygments.lexers.textedit',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\textedit.py',
   'PYMODULE'),
  ('pygments.lexers.textfmts',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\textfmts.py',
   'PYMODULE'),
  ('pygments.lexers.theorem',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\theorem.py',
   'PYMODULE'),
  ('pygments.lexers.thingsdb',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\thingsdb.py',
   'PYMODULE'),
  ('pygments.lexers.tlb',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\tlb.py',
   'PYMODULE'),
  ('pygments.lexers.tls',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\tls.py',
   'PYMODULE'),
  ('pygments.lexers.tnt',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\tnt.py',
   'PYMODULE'),
  ('pygments.lexers.trafficscript',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\trafficscript.py',
   'PYMODULE'),
  ('pygments.lexers.typoscript',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\typoscript.py',
   'PYMODULE'),
  ('pygments.lexers.typst',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\typst.py',
   'PYMODULE'),
  ('pygments.lexers.ul4',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\ul4.py',
   'PYMODULE'),
  ('pygments.lexers.unicon',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\unicon.py',
   'PYMODULE'),
  ('pygments.lexers.urbi',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\urbi.py',
   'PYMODULE'),
  ('pygments.lexers.usd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\usd.py',
   'PYMODULE'),
  ('pygments.lexers.varnish',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\varnish.py',
   'PYMODULE'),
  ('pygments.lexers.verification',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\verification.py',
   'PYMODULE'),
  ('pygments.lexers.verifpal',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\verifpal.py',
   'PYMODULE'),
  ('pygments.lexers.vip',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\vip.py',
   'PYMODULE'),
  ('pygments.lexers.vyper',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\vyper.py',
   'PYMODULE'),
  ('pygments.lexers.web',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\web.py',
   'PYMODULE'),
  ('pygments.lexers.webassembly',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\webassembly.py',
   'PYMODULE'),
  ('pygments.lexers.webidl',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\webidl.py',
   'PYMODULE'),
  ('pygments.lexers.webmisc',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\webmisc.py',
   'PYMODULE'),
  ('pygments.lexers.wgsl',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\wgsl.py',
   'PYMODULE'),
  ('pygments.lexers.whiley',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\whiley.py',
   'PYMODULE'),
  ('pygments.lexers.wowtoc',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\wowtoc.py',
   'PYMODULE'),
  ('pygments.lexers.wren',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\wren.py',
   'PYMODULE'),
  ('pygments.lexers.x10',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\x10.py',
   'PYMODULE'),
  ('pygments.lexers.xorg',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\xorg.py',
   'PYMODULE'),
  ('pygments.lexers.yang',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\yang.py',
   'PYMODULE'),
  ('pygments.lexers.yara',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\yara.py',
   'PYMODULE'),
  ('pygments.lexers.zig',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\lexers\\zig.py',
   'PYMODULE'),
  ('pygments.modeline',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\modeline.py',
   'PYMODULE'),
  ('pygments.plugin',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\plugin.py',
   'PYMODULE'),
  ('pygments.regexopt',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\regexopt.py',
   'PYMODULE'),
  ('pygments.scanner',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\scanner.py',
   'PYMODULE'),
  ('pygments.style',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\style.py',
   'PYMODULE'),
  ('pygments.styles',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\__init__.py',
   'PYMODULE'),
  ('pygments.styles._mapping',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\_mapping.py',
   'PYMODULE'),
  ('pygments.styles.abap',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\abap.py',
   'PYMODULE'),
  ('pygments.styles.algol',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\algol.py',
   'PYMODULE'),
  ('pygments.styles.algol_nu',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\algol_nu.py',
   'PYMODULE'),
  ('pygments.styles.arduino',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\arduino.py',
   'PYMODULE'),
  ('pygments.styles.autumn',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\autumn.py',
   'PYMODULE'),
  ('pygments.styles.borland',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\borland.py',
   'PYMODULE'),
  ('pygments.styles.bw',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\bw.py',
   'PYMODULE'),
  ('pygments.styles.coffee',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\coffee.py',
   'PYMODULE'),
  ('pygments.styles.colorful',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\colorful.py',
   'PYMODULE'),
  ('pygments.styles.default',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\default.py',
   'PYMODULE'),
  ('pygments.styles.dracula',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\dracula.py',
   'PYMODULE'),
  ('pygments.styles.emacs',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\emacs.py',
   'PYMODULE'),
  ('pygments.styles.friendly',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\friendly.py',
   'PYMODULE'),
  ('pygments.styles.friendly_grayscale',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\friendly_grayscale.py',
   'PYMODULE'),
  ('pygments.styles.fruity',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\fruity.py',
   'PYMODULE'),
  ('pygments.styles.gh_dark',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\gh_dark.py',
   'PYMODULE'),
  ('pygments.styles.gruvbox',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\gruvbox.py',
   'PYMODULE'),
  ('pygments.styles.igor',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\igor.py',
   'PYMODULE'),
  ('pygments.styles.inkpot',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\inkpot.py',
   'PYMODULE'),
  ('pygments.styles.lightbulb',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\lightbulb.py',
   'PYMODULE'),
  ('pygments.styles.lilypond',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\lilypond.py',
   'PYMODULE'),
  ('pygments.styles.lovelace',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\lovelace.py',
   'PYMODULE'),
  ('pygments.styles.manni',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\manni.py',
   'PYMODULE'),
  ('pygments.styles.material',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\material.py',
   'PYMODULE'),
  ('pygments.styles.monokai',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\monokai.py',
   'PYMODULE'),
  ('pygments.styles.murphy',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\murphy.py',
   'PYMODULE'),
  ('pygments.styles.native',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\native.py',
   'PYMODULE'),
  ('pygments.styles.nord',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\nord.py',
   'PYMODULE'),
  ('pygments.styles.onedark',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\onedark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_dark',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\paraiso_dark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_light',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\paraiso_light.py',
   'PYMODULE'),
  ('pygments.styles.pastie',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\pastie.py',
   'PYMODULE'),
  ('pygments.styles.perldoc',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\perldoc.py',
   'PYMODULE'),
  ('pygments.styles.rainbow_dash',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\rainbow_dash.py',
   'PYMODULE'),
  ('pygments.styles.rrt',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\rrt.py',
   'PYMODULE'),
  ('pygments.styles.sas',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\sas.py',
   'PYMODULE'),
  ('pygments.styles.solarized',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\solarized.py',
   'PYMODULE'),
  ('pygments.styles.staroffice',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\staroffice.py',
   'PYMODULE'),
  ('pygments.styles.stata_dark',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\stata_dark.py',
   'PYMODULE'),
  ('pygments.styles.stata_light',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\stata_light.py',
   'PYMODULE'),
  ('pygments.styles.tango',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\tango.py',
   'PYMODULE'),
  ('pygments.styles.trac',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\trac.py',
   'PYMODULE'),
  ('pygments.styles.vim',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\vim.py',
   'PYMODULE'),
  ('pygments.styles.vs',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\vs.py',
   'PYMODULE'),
  ('pygments.styles.xcode',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\xcode.py',
   'PYMODULE'),
  ('pygments.styles.zenburn',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\styles\\zenburn.py',
   'PYMODULE'),
  ('pygments.token',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\token.py',
   'PYMODULE'),
  ('pygments.unistring',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\unistring.py',
   'PYMODULE'),
  ('pygments.util',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pygments\\util.py',
   'PYMODULE'),
  ('pyperclip',
   'D:\\Python\\Python39-32\\lib\\site-packages\\pyperclip\\__init__.py',
   'PYMODULE'),
  ('queue', 'D:\\Python\\Python39-32\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\Python\\Python39-32\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\Python\\Python39-32\\lib\\random.py', 'PYMODULE'),
  ('repath',
   'D:\\Python\\Python39-32\\lib\\site-packages\\repath.py',
   'PYMODULE'),
  ('rich',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\__init__.py',
   'PYMODULE'),
  ('rich.__main__',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\__main__.py',
   'PYMODULE'),
  ('rich._cell_widths',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\_cell_widths.py',
   'PYMODULE'),
  ('rich._emoji_codes',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\_emoji_codes.py',
   'PYMODULE'),
  ('rich._emoji_replace',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\_emoji_replace.py',
   'PYMODULE'),
  ('rich._export_format',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\_export_format.py',
   'PYMODULE'),
  ('rich._extension',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\_extension.py',
   'PYMODULE'),
  ('rich._fileno',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\_fileno.py',
   'PYMODULE'),
  ('rich._inspect',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\_inspect.py',
   'PYMODULE'),
  ('rich._log_render',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\_log_render.py',
   'PYMODULE'),
  ('rich._loop',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\_loop.py',
   'PYMODULE'),
  ('rich._null_file',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\_null_file.py',
   'PYMODULE'),
  ('rich._palettes',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\_palettes.py',
   'PYMODULE'),
  ('rich._pick',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\_pick.py',
   'PYMODULE'),
  ('rich._ratio',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\_ratio.py',
   'PYMODULE'),
  ('rich._spinners',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\_spinners.py',
   'PYMODULE'),
  ('rich._stack',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\_stack.py',
   'PYMODULE'),
  ('rich._timer',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\_timer.py',
   'PYMODULE'),
  ('rich._win32_console',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\_win32_console.py',
   'PYMODULE'),
  ('rich._windows',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\_windows.py',
   'PYMODULE'),
  ('rich._windows_renderer',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\_windows_renderer.py',
   'PYMODULE'),
  ('rich._wrap',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\_wrap.py',
   'PYMODULE'),
  ('rich.abc',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\abc.py',
   'PYMODULE'),
  ('rich.align',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\align.py',
   'PYMODULE'),
  ('rich.ansi',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\ansi.py',
   'PYMODULE'),
  ('rich.box',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\box.py',
   'PYMODULE'),
  ('rich.cells',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\cells.py',
   'PYMODULE'),
  ('rich.color',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\color.py',
   'PYMODULE'),
  ('rich.color_triplet',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\color_triplet.py',
   'PYMODULE'),
  ('rich.columns',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\columns.py',
   'PYMODULE'),
  ('rich.console',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\console.py',
   'PYMODULE'),
  ('rich.constrain',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\constrain.py',
   'PYMODULE'),
  ('rich.containers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\containers.py',
   'PYMODULE'),
  ('rich.control',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\control.py',
   'PYMODULE'),
  ('rich.default_styles',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\default_styles.py',
   'PYMODULE'),
  ('rich.emoji',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\emoji.py',
   'PYMODULE'),
  ('rich.errors',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\errors.py',
   'PYMODULE'),
  ('rich.file_proxy',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\file_proxy.py',
   'PYMODULE'),
  ('rich.filesize',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\filesize.py',
   'PYMODULE'),
  ('rich.highlighter',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\highlighter.py',
   'PYMODULE'),
  ('rich.json',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\json.py',
   'PYMODULE'),
  ('rich.jupyter',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\jupyter.py',
   'PYMODULE'),
  ('rich.live',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\live.py',
   'PYMODULE'),
  ('rich.live_render',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\live_render.py',
   'PYMODULE'),
  ('rich.markdown',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\markdown.py',
   'PYMODULE'),
  ('rich.markup',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\markup.py',
   'PYMODULE'),
  ('rich.measure',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\measure.py',
   'PYMODULE'),
  ('rich.padding',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\padding.py',
   'PYMODULE'),
  ('rich.pager',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\pager.py',
   'PYMODULE'),
  ('rich.palette',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\palette.py',
   'PYMODULE'),
  ('rich.panel',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\panel.py',
   'PYMODULE'),
  ('rich.pretty',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\pretty.py',
   'PYMODULE'),
  ('rich.progress',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\progress.py',
   'PYMODULE'),
  ('rich.progress_bar',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\progress_bar.py',
   'PYMODULE'),
  ('rich.protocol',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\protocol.py',
   'PYMODULE'),
  ('rich.region',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\region.py',
   'PYMODULE'),
  ('rich.repr',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\repr.py',
   'PYMODULE'),
  ('rich.rule',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\rule.py',
   'PYMODULE'),
  ('rich.scope',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\scope.py',
   'PYMODULE'),
  ('rich.screen',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\screen.py',
   'PYMODULE'),
  ('rich.segment',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\segment.py',
   'PYMODULE'),
  ('rich.spinner',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\spinner.py',
   'PYMODULE'),
  ('rich.status',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\status.py',
   'PYMODULE'),
  ('rich.style',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\style.py',
   'PYMODULE'),
  ('rich.styled',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\styled.py',
   'PYMODULE'),
  ('rich.syntax',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\syntax.py',
   'PYMODULE'),
  ('rich.table',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\table.py',
   'PYMODULE'),
  ('rich.terminal_theme',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\terminal_theme.py',
   'PYMODULE'),
  ('rich.text',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\text.py',
   'PYMODULE'),
  ('rich.theme',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\theme.py',
   'PYMODULE'),
  ('rich.themes',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\themes.py',
   'PYMODULE'),
  ('rich.traceback',
   'D:\\Python\\Python39-32\\lib\\site-packages\\rich\\traceback.py',
   'PYMODULE'),
  ('rlcompleter', 'D:\\Python\\Python39-32\\lib\\rlcompleter.py', 'PYMODULE'),
  ('runpy', 'D:\\Python\\Python39-32\\lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\Python\\Python39-32\\lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\Python\\Python39-32\\lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._deprecation_warning',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\_deprecation_warning.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.__about__',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._compat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._typing',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_typing.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\_vendor\\pyparsing.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\config.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.extern',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\py34compat.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\Python\\Python39-32\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'D:\\Python\\Python39-32\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\Python\\Python39-32\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\Python\\Python39-32\\lib\\signal.py', 'PYMODULE'),
  ('site', 'D:\\Python\\Python39-32\\lib\\site.py', 'PYMODULE'),
  ('six', 'D:\\Python\\Python39-32\\lib\\site-packages\\six.py', 'PYMODULE'),
  ('sniffio',
   'D:\\Python\\Python39-32\\lib\\site-packages\\sniffio\\__init__.py',
   'PYMODULE'),
  ('sniffio._impl',
   'D:\\Python\\Python39-32\\lib\\site-packages\\sniffio\\_impl.py',
   'PYMODULE'),
  ('sniffio._version',
   'D:\\Python\\Python39-32\\lib\\site-packages\\sniffio\\_version.py',
   'PYMODULE'),
  ('socket', 'D:\\Python\\Python39-32\\lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'D:\\Python\\Python39-32\\lib\\socketserver.py', 'PYMODULE'),
  ('sortedcontainers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\sortedcontainers\\__init__.py',
   'PYMODULE'),
  ('sortedcontainers.sorteddict',
   'D:\\Python\\Python39-32\\lib\\site-packages\\sortedcontainers\\sorteddict.py',
   'PYMODULE'),
  ('sortedcontainers.sortedlist',
   'D:\\Python\\Python39-32\\lib\\site-packages\\sortedcontainers\\sortedlist.py',
   'PYMODULE'),
  ('sortedcontainers.sortedset',
   'D:\\Python\\Python39-32\\lib\\site-packages\\sortedcontainers\\sortedset.py',
   'PYMODULE'),
  ('ssl', 'D:\\Python\\Python39-32\\lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\Python\\Python39-32\\lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\Python\\Python39-32\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\Python\\Python39-32\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\Python\\Python39-32\\lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'D:\\Python\\Python39-32\\lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'D:\\Python\\Python39-32\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\Python\\Python39-32\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\Python\\Python39-32\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\Python\\Python39-32\\lib\\threading.py', 'PYMODULE'),
  ('token', 'D:\\Python\\Python39-32\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\Python\\Python39-32\\lib\\tokenize.py', 'PYMODULE'),
  ('tomli',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tomli\\__init__.py',
   'PYMODULE'),
  ('tomli._parser',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tomli\\_parser.py',
   'PYMODULE'),
  ('tomli._re',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tomli\\_re.py',
   'PYMODULE'),
  ('tomli._types',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tomli\\_types.py',
   'PYMODULE'),
  ('tracemalloc', 'D:\\Python\\Python39-32\\lib\\tracemalloc.py', 'PYMODULE'),
  ('trio',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\__init__.py',
   'PYMODULE'),
  ('trio._abc',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_abc.py',
   'PYMODULE'),
  ('trio._channel',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_channel.py',
   'PYMODULE'),
  ('trio._core',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\__init__.py',
   'PYMODULE'),
  ('trio._core._asyncgens',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\_asyncgens.py',
   'PYMODULE'),
  ('trio._core._concat_tb',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\_concat_tb.py',
   'PYMODULE'),
  ('trio._core._entry_queue',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\_entry_queue.py',
   'PYMODULE'),
  ('trio._core._exceptions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\_exceptions.py',
   'PYMODULE'),
  ('trio._core._generated_instrumentation',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\_generated_instrumentation.py',
   'PYMODULE'),
  ('trio._core._generated_io_epoll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\_generated_io_epoll.py',
   'PYMODULE'),
  ('trio._core._generated_io_kqueue',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\_generated_io_kqueue.py',
   'PYMODULE'),
  ('trio._core._generated_io_windows',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\_generated_io_windows.py',
   'PYMODULE'),
  ('trio._core._generated_run',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\_generated_run.py',
   'PYMODULE'),
  ('trio._core._instrumentation',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\_instrumentation.py',
   'PYMODULE'),
  ('trio._core._io_common',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\_io_common.py',
   'PYMODULE'),
  ('trio._core._io_epoll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\_io_epoll.py',
   'PYMODULE'),
  ('trio._core._io_kqueue',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\_io_kqueue.py',
   'PYMODULE'),
  ('trio._core._io_windows',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\_io_windows.py',
   'PYMODULE'),
  ('trio._core._ki',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\_ki.py',
   'PYMODULE'),
  ('trio._core._local',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\_local.py',
   'PYMODULE'),
  ('trio._core._mock_clock',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\_mock_clock.py',
   'PYMODULE'),
  ('trio._core._parking_lot',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\_parking_lot.py',
   'PYMODULE'),
  ('trio._core._run',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\_run.py',
   'PYMODULE'),
  ('trio._core._run_context',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\_run_context.py',
   'PYMODULE'),
  ('trio._core._thread_cache',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\_thread_cache.py',
   'PYMODULE'),
  ('trio._core._traps',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\_traps.py',
   'PYMODULE'),
  ('trio._core._unbounded_queue',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\_unbounded_queue.py',
   'PYMODULE'),
  ('trio._core._wakeup_socketpair',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\_wakeup_socketpair.py',
   'PYMODULE'),
  ('trio._core._windows_cffi',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_core\\_windows_cffi.py',
   'PYMODULE'),
  ('trio._deprecate',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_deprecate.py',
   'PYMODULE'),
  ('trio._dtls',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_dtls.py',
   'PYMODULE'),
  ('trio._file_io',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_file_io.py',
   'PYMODULE'),
  ('trio._highlevel_generic',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_highlevel_generic.py',
   'PYMODULE'),
  ('trio._highlevel_open_tcp_listeners',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_highlevel_open_tcp_listeners.py',
   'PYMODULE'),
  ('trio._highlevel_open_tcp_stream',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_highlevel_open_tcp_stream.py',
   'PYMODULE'),
  ('trio._highlevel_open_unix_stream',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_highlevel_open_unix_stream.py',
   'PYMODULE'),
  ('trio._highlevel_serve_listeners',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_highlevel_serve_listeners.py',
   'PYMODULE'),
  ('trio._highlevel_socket',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_highlevel_socket.py',
   'PYMODULE'),
  ('trio._highlevel_ssl_helpers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_highlevel_ssl_helpers.py',
   'PYMODULE'),
  ('trio._path',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_path.py',
   'PYMODULE'),
  ('trio._signals',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_signals.py',
   'PYMODULE'),
  ('trio._socket',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_socket.py',
   'PYMODULE'),
  ('trio._ssl',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_ssl.py',
   'PYMODULE'),
  ('trio._subprocess',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_subprocess.py',
   'PYMODULE'),
  ('trio._subprocess_platform',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_subprocess_platform\\__init__.py',
   'PYMODULE'),
  ('trio._subprocess_platform.kqueue',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_subprocess_platform\\kqueue.py',
   'PYMODULE'),
  ('trio._subprocess_platform.waitid',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_subprocess_platform\\waitid.py',
   'PYMODULE'),
  ('trio._subprocess_platform.windows',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_subprocess_platform\\windows.py',
   'PYMODULE'),
  ('trio._sync',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_sync.py',
   'PYMODULE'),
  ('trio._threads',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_threads.py',
   'PYMODULE'),
  ('trio._timeouts',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_timeouts.py',
   'PYMODULE'),
  ('trio._unix_pipes',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_unix_pipes.py',
   'PYMODULE'),
  ('trio._util',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_util.py',
   'PYMODULE'),
  ('trio._version',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_version.py',
   'PYMODULE'),
  ('trio._wait_for_object',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_wait_for_object.py',
   'PYMODULE'),
  ('trio._windows_pipes',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\_windows_pipes.py',
   'PYMODULE'),
  ('trio.abc',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\abc.py',
   'PYMODULE'),
  ('trio.from_thread',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\from_thread.py',
   'PYMODULE'),
  ('trio.lowlevel',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\lowlevel.py',
   'PYMODULE'),
  ('trio.socket',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\socket.py',
   'PYMODULE'),
  ('trio.testing',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\testing\\__init__.py',
   'PYMODULE'),
  ('trio.testing._check_streams',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\testing\\_check_streams.py',
   'PYMODULE'),
  ('trio.testing._checkpoints',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\testing\\_checkpoints.py',
   'PYMODULE'),
  ('trio.testing._memory_streams',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\testing\\_memory_streams.py',
   'PYMODULE'),
  ('trio.testing._network',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\testing\\_network.py',
   'PYMODULE'),
  ('trio.testing._raises_group',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\testing\\_raises_group.py',
   'PYMODULE'),
  ('trio.testing._sequencer',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\testing\\_sequencer.py',
   'PYMODULE'),
  ('trio.testing._trio_test',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\testing\\_trio_test.py',
   'PYMODULE'),
  ('trio.to_thread',
   'D:\\Python\\Python39-32\\lib\\site-packages\\trio\\to_thread.py',
   'PYMODULE'),
  ('tty', 'D:\\Python\\Python39-32\\lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\Python\\Python39-32\\lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('typing_inspection',
   'D:\\Python\\Python39-32\\lib\\site-packages\\typing_inspection\\__init__.py',
   'PYMODULE'),
  ('typing_inspection.introspection',
   'D:\\Python\\Python39-32\\lib\\site-packages\\typing_inspection\\introspection.py',
   'PYMODULE'),
  ('typing_inspection.typing_objects',
   'D:\\Python\\Python39-32\\lib\\site-packages\\typing_inspection\\typing_objects.py',
   'PYMODULE'),
  ('tzdata',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tzdata\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tzdata\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Africa',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tzdata\\zoneinfo\\America\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Argentina',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Indiana',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Kentucky',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.North_Dakota',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Antarctica',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Arctic',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tzdata\\zoneinfo\\Arctic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Asia',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Atlantic',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Australia',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Brazil',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Canada',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tzdata\\zoneinfo\\Canada\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Chile',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tzdata\\zoneinfo\\Chile\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Etc',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Europe',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Indian',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Mexico',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Pacific',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.US',
   'D:\\Python\\Python39-32\\lib\\site-packages\\tzdata\\zoneinfo\\US\\__init__.py',
   'PYMODULE'),
  ('unittest',
   'D:\\Python\\Python39-32\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\Python\\Python39-32\\lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\Python\\Python39-32\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\Python\\Python39-32\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\Python\\Python39-32\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\Python\\Python39-32\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\Python\\Python39-32\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\Python\\Python39-32\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\Python\\Python39-32\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\Python\\Python39-32\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\Python\\Python39-32\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib', 'D:\\Python\\Python39-32\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error',
   'D:\\Python\\Python39-32\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\Python\\Python39-32\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\Python\\Python39-32\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\Python\\Python39-32\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uu', 'D:\\Python\\Python39-32\\lib\\uu.py', 'PYMODULE'),
  ('uuid', 'D:\\Python\\Python39-32\\lib\\uuid.py', 'PYMODULE'),
  ('volcenginesdkark',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkark.api',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\api\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkark.api.ark_api',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\api\\ark_api.py',
   'PYMODULE'),
  ('volcenginesdkark.models',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkark.models.create_batch_inference_job_request',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\create_batch_inference_job_request.py',
   'PYMODULE'),
  ('volcenginesdkark.models.create_batch_inference_job_response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\create_batch_inference_job_response.py',
   'PYMODULE'),
  ('volcenginesdkark.models.create_endpoint_request',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\create_endpoint_request.py',
   'PYMODULE'),
  ('volcenginesdkark.models.create_endpoint_response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\create_endpoint_response.py',
   'PYMODULE'),
  ('volcenginesdkark.models.create_evaluation_job_request',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\create_evaluation_job_request.py',
   'PYMODULE'),
  ('volcenginesdkark.models.create_evaluation_job_response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\create_evaluation_job_response.py',
   'PYMODULE'),
  ('volcenginesdkark.models.create_model_customization_job_request',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\create_model_customization_job_request.py',
   'PYMODULE'),
  ('volcenginesdkark.models.create_model_customization_job_response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\create_model_customization_job_response.py',
   'PYMODULE'),
  ('volcenginesdkark.models.data_for_create_model_customization_job_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\data_for_create_model_customization_job_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.data_for_get_model_customization_job_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\data_for_get_model_customization_job_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.data_for_list_model_customization_jobs_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\data_for_list_model_customization_jobs_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.dataset_for_create_model_customization_job_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\dataset_for_create_model_customization_job_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.dataset_for_get_model_customization_job_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\dataset_for_get_model_customization_job_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.dataset_for_list_model_customization_jobs_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\dataset_for_list_model_customization_jobs_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.delete_endpoint_request',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\delete_endpoint_request.py',
   'PYMODULE'),
  ('volcenginesdkark.models.delete_endpoint_response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\delete_endpoint_response.py',
   'PYMODULE'),
  ('volcenginesdkark.models.evaluation_dataset_for_create_evaluation_job_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\evaluation_dataset_for_create_evaluation_job_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.filter_for_list_batch_inference_jobs_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\filter_for_list_batch_inference_jobs_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.filter_for_list_endpoints_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\filter_for_list_endpoints_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.filter_for_list_model_customization_jobs_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\filter_for_list_model_customization_jobs_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.foundation_model_for_create_batch_inference_job_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\foundation_model_for_create_batch_inference_job_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.foundation_model_for_create_endpoint_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\foundation_model_for_create_endpoint_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.foundation_model_for_create_model_customization_job_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\foundation_model_for_create_model_customization_job_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.foundation_model_for_get_endpoint_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\foundation_model_for_get_endpoint_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.foundation_model_for_get_model_customization_job_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\foundation_model_for_get_model_customization_job_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.foundation_model_for_list_batch_inference_jobs_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\foundation_model_for_list_batch_inference_jobs_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.foundation_model_for_list_batch_inference_jobs_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\foundation_model_for_list_batch_inference_jobs_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.foundation_model_for_list_endpoints_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\foundation_model_for_list_endpoints_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.foundation_model_for_list_model_customization_jobs_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\foundation_model_for_list_model_customization_jobs_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.foundation_model_for_list_model_customization_jobs_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\foundation_model_for_list_model_customization_jobs_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.get_api_key_request',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\get_api_key_request.py',
   'PYMODULE'),
  ('volcenginesdkark.models.get_api_key_response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\get_api_key_response.py',
   'PYMODULE'),
  ('volcenginesdkark.models.get_endpoint_certificate_request',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\get_endpoint_certificate_request.py',
   'PYMODULE'),
  ('volcenginesdkark.models.get_endpoint_certificate_response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\get_endpoint_certificate_response.py',
   'PYMODULE'),
  ('volcenginesdkark.models.get_endpoint_request',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\get_endpoint_request.py',
   'PYMODULE'),
  ('volcenginesdkark.models.get_endpoint_response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\get_endpoint_response.py',
   'PYMODULE'),
  ('volcenginesdkark.models.get_model_customization_job_metric_data_request',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\get_model_customization_job_metric_data_request.py',
   'PYMODULE'),
  ('volcenginesdkark.models.get_model_customization_job_metric_data_response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\get_model_customization_job_metric_data_response.py',
   'PYMODULE'),
  ('volcenginesdkark.models.get_model_customization_job_metrics_request',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\get_model_customization_job_metrics_request.py',
   'PYMODULE'),
  ('volcenginesdkark.models.get_model_customization_job_metrics_response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\get_model_customization_job_metrics_response.py',
   'PYMODULE'),
  ('volcenginesdkark.models.get_model_customization_job_request',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\get_model_customization_job_request.py',
   'PYMODULE'),
  ('volcenginesdkark.models.get_model_customization_job_response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\get_model_customization_job_response.py',
   'PYMODULE'),
  ('volcenginesdkark.models.hyperparameter_for_create_model_customization_job_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\hyperparameter_for_create_model_customization_job_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.hyperparameter_for_get_model_customization_job_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\hyperparameter_for_get_model_customization_job_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.hyperparameter_for_list_model_customization_jobs_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\hyperparameter_for_list_model_customization_jobs_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.input_file_tos_location_for_create_batch_inference_job_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\input_file_tos_location_for_create_batch_inference_job_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.input_file_tos_location_for_list_batch_inference_jobs_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\input_file_tos_location_for_list_batch_inference_jobs_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.item_for_get_model_customization_job_metric_data_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\item_for_get_model_customization_job_metric_data_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.item_for_list_batch_inference_jobs_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\item_for_list_batch_inference_jobs_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.item_for_list_endpoints_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\item_for_list_endpoints_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.item_for_list_model_customization_jobs_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\item_for_list_model_customization_jobs_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.list_batch_inference_jobs_request',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\list_batch_inference_jobs_request.py',
   'PYMODULE'),
  ('volcenginesdkark.models.list_batch_inference_jobs_response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\list_batch_inference_jobs_response.py',
   'PYMODULE'),
  ('volcenginesdkark.models.list_endpoints_request',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\list_endpoints_request.py',
   'PYMODULE'),
  ('volcenginesdkark.models.list_endpoints_response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\list_endpoints_response.py',
   'PYMODULE'),
  ('volcenginesdkark.models.list_model_customization_jobs_request',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\list_model_customization_jobs_request.py',
   'PYMODULE'),
  ('volcenginesdkark.models.list_model_customization_jobs_response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\list_model_customization_jobs_response.py',
   'PYMODULE'),
  ('volcenginesdkark.models.model_reference_for_create_batch_inference_job_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\model_reference_for_create_batch_inference_job_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.model_reference_for_create_endpoint_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\model_reference_for_create_endpoint_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.model_reference_for_create_model_customization_job_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\model_reference_for_create_model_customization_job_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.model_reference_for_get_endpoint_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\model_reference_for_get_endpoint_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.model_reference_for_get_model_customization_job_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\model_reference_for_get_model_customization_job_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.model_reference_for_list_batch_inference_jobs_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\model_reference_for_list_batch_inference_jobs_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.model_reference_for_list_endpoints_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\model_reference_for_list_endpoints_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.model_reference_for_list_model_customization_jobs_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\model_reference_for_list_model_customization_jobs_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.moderation_for_create_endpoint_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\moderation_for_create_endpoint_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.moderation_for_get_endpoint_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\moderation_for_get_endpoint_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.output_dir_tos_location_for_create_batch_inference_job_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\output_dir_tos_location_for_create_batch_inference_job_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.output_dir_tos_location_for_list_batch_inference_jobs_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\output_dir_tos_location_for_list_batch_inference_jobs_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.output_for_get_model_customization_job_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\output_for_get_model_customization_job_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.output_for_list_model_customization_jobs_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\output_for_list_model_customization_jobs_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.preset_dataset_for_create_model_customization_job_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\preset_dataset_for_create_model_customization_job_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.preset_dataset_for_get_model_customization_job_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\preset_dataset_for_get_model_customization_job_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.preset_dataset_for_list_model_customization_jobs_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\preset_dataset_for_list_model_customization_jobs_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.rate_limit_for_create_endpoint_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\rate_limit_for_create_endpoint_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.rate_limit_for_get_endpoint_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\rate_limit_for_get_endpoint_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.rate_limit_for_list_endpoints_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\rate_limit_for_list_endpoints_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.request_counts_for_list_batch_inference_jobs_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\request_counts_for_list_batch_inference_jobs_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.resume_model_customization_job_request',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\resume_model_customization_job_request.py',
   'PYMODULE'),
  ('volcenginesdkark.models.resume_model_customization_job_response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\resume_model_customization_job_response.py',
   'PYMODULE'),
  ('volcenginesdkark.models.sample_for_get_model_customization_job_metric_data_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\sample_for_get_model_customization_job_metric_data_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.status_for_get_model_customization_job_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\status_for_get_model_customization_job_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.status_for_list_batch_inference_jobs_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\status_for_list_batch_inference_jobs_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.status_for_list_model_customization_jobs_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\status_for_list_model_customization_jobs_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.stop_endpoint_request',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\stop_endpoint_request.py',
   'PYMODULE'),
  ('volcenginesdkark.models.stop_endpoint_response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\stop_endpoint_response.py',
   'PYMODULE'),
  ('volcenginesdkark.models.tag_filter_for_list_batch_inference_jobs_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\tag_filter_for_list_batch_inference_jobs_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.tag_filter_for_list_endpoints_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\tag_filter_for_list_endpoints_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.tag_filter_for_list_model_customization_jobs_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\tag_filter_for_list_model_customization_jobs_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.tag_for_create_batch_inference_job_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\tag_for_create_batch_inference_job_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.tag_for_create_endpoint_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\tag_for_create_endpoint_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.tag_for_create_evaluation_job_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\tag_for_create_evaluation_job_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.tag_for_create_model_customization_job_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\tag_for_create_model_customization_job_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.tag_for_get_endpoint_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\tag_for_get_endpoint_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.tag_for_get_model_customization_job_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\tag_for_get_model_customization_job_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.tag_for_list_batch_inference_jobs_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\tag_for_list_batch_inference_jobs_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.tag_for_list_endpoints_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\tag_for_list_endpoints_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.tag_for_list_model_customization_jobs_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\tag_for_list_model_customization_jobs_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.terminate_model_customization_job_request',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\terminate_model_customization_job_request.py',
   'PYMODULE'),
  ('volcenginesdkark.models.terminate_model_customization_job_response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\terminate_model_customization_job_response.py',
   'PYMODULE'),
  ('volcenginesdkark.models.tos_location_for_create_evaluation_job_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\tos_location_for_create_evaluation_job_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.training_set_for_create_model_customization_job_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\training_set_for_create_model_customization_job_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.training_set_for_get_model_customization_job_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\training_set_for_get_model_customization_job_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.training_set_for_list_model_customization_jobs_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\training_set_for_list_model_customization_jobs_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.validation_set_for_create_model_customization_job_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\validation_set_for_create_model_customization_job_input.py',
   'PYMODULE'),
  ('volcenginesdkark.models.validation_set_for_get_model_customization_job_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\validation_set_for_get_model_customization_job_output.py',
   'PYMODULE'),
  ('volcenginesdkark.models.validation_set_for_list_model_customization_jobs_output',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkark\\models\\validation_set_for_list_model_customization_jobs_output.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime._base_client',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\_base_client.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime._client',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\_client.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime._compat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\_compat.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime._constants',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\_constants.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime._exceptions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\_exceptions.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime._files',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\_files.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime._legacy_response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\_legacy_response.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime._models',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\_models.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime._request_options',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\_request_options.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime._resource',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\_resource.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime._response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\_response.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime._streaming',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\_streaming.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime._types',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\_types.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime._utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\_utils\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime._utils._key_agreement',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\_utils\\_key_agreement.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime._utils._logs',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\_utils\\_logs.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime._utils._model_breaker',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\_utils\\_model_breaker.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime._utils._streams',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\_utils\\_streams.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime._utils._transform',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\_utils\\_transform.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime._utils._typing',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\_utils\\_typing.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime._utils._utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\_utils\\_utils.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.common',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\common\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.common._parsing',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\common\\_parsing\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.common._parsing._completions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\common\\_parsing\\_completions.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.common._pydantic',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\common\\_pydantic.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.common._tools',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\common\\_tools.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.common.streaming',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\common\\streaming\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.common.streaming._deltas',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\common\\streaming\\_deltas.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.common.streaming.chat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\common\\streaming\\chat\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.common.streaming.chat._completions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\common\\streaming\\chat\\_completions.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.common.streaming.chat._events',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\common\\streaming\\chat\\_events.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.common.streaming.chat._types',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\common\\streaming\\chat\\_types.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.batch_chat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\batch_chat\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.batch_chat.chat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\batch_chat\\chat.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.batch_chat.completions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\batch_chat\\completions.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.beta',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\beta\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.beta.beta',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\beta\\beta.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.beta.chat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\beta\\chat\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.beta.chat.chat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\beta\\chat\\chat.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.beta.chat.completions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\beta\\chat\\completions.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.bot',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\bot\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.bot.chat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\bot\\chat.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.bot.completions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\bot\\completions.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.chat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\chat\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.chat.chat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\chat\\chat.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.chat.completions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\chat\\completions.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.classification',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\classification.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.content_generation',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\content_generation\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.content_generation.content_generation',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\content_generation\\content_generation.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.content_generation.tasks',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\content_generation\\tasks.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.context',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\context\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.context.completions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\context\\completions.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.context.context',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\context\\context.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.embeddings',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\embeddings.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.images',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\images\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.images.images',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\images\\images.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.multimodal_embeddings',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\multimodal_embeddings.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.resources.tokenization',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\resources\\tokenization.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.bot_chat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\bot_chat\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.bot_chat.bot_chat_completion',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\bot_chat\\bot_chat_completion.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.bot_chat.bot_chat_completion_chunk',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\bot_chat\\bot_chat_completion_chunk.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.bot_chat.bot_reference',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\bot_chat\\bot_reference.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.bot_chat.bot_usage',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\bot_chat\\bot_usage.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.chat_completion',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\chat_completion.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.chat_completion_assistant_message_param',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\chat_completion_assistant_message_param.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.chat_completion_audio',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\chat_completion_audio.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.chat_completion_chunk',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\chat_completion_chunk.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.chat_completion_content_part_image_param',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\chat_completion_content_part_image_param.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.chat_completion_content_part_param',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\chat_completion_content_part_param.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.chat_completion_content_part_text_param',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\chat_completion_content_part_text_param.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.chat_completion_content_part_video_param',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\chat_completion_content_part_video_param.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.chat_completion_function_call_option_param',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\chat_completion_function_call_option_param.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.chat_completion_function_message_param',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\chat_completion_function_message_param.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.chat_completion_message',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\chat_completion_message.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.chat_completion_message_param',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\chat_completion_message_param.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.chat_completion_message_tool_call',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\chat_completion_message_tool_call.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.chat_completion_message_tool_call_param',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\chat_completion_message_tool_call_param.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.chat_completion_named_tool_choice_param',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\chat_completion_named_tool_choice_param.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.chat_completion_role',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\chat_completion_role.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.chat_completion_stream_options_param',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\chat_completion_stream_options_param.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.chat_completion_system_message_param',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\chat_completion_system_message_param.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.chat_completion_token_logprob',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\chat_completion_token_logprob.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.chat_completion_tool_choice_option_param',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\chat_completion_tool_choice_option_param.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.chat_completion_tool_message_param',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\chat_completion_tool_message_param.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.chat_completion_tool_param',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\chat_completion_tool_param.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.chat_completion_user_message_param',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\chat_completion_user_message_param.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.completion_create_params',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\completion_create_params.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.parsed_chat_completion',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\parsed_chat_completion.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.chat.parsed_function_tool_call',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\chat\\parsed_function_tool_call.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.completion_usage',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\completion_usage.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.content_generation',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\content_generation\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.content_generation.content_generation_task',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\content_generation\\content_generation_task.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.content_generation.content_generation_task_id',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\content_generation\\content_generation_task_id.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.content_generation.create_task_content_param',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\content_generation\\create_task_content_param.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.content_generation.list_content_generation_tasks_response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\content_generation\\list_content_generation_tasks_response.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.context',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\context\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.context.context_chat_completion',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\context\\context_chat_completion.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.context.context_chat_completion_chunk',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\context\\context_chat_completion_chunk.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.context.context_create_params',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\context\\context_create_params.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.context.create_context_response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\context\\create_context_response.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.context.truncation_strategy',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\context\\truncation_strategy.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.create_classification_response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\create_classification_response.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.create_embedding_response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\create_embedding_response.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.create_tokenization_response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\create_tokenization_response.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.embedding',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\embedding.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.images',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\images\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.images.images',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\images\\images.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.multimodal_embedding',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\multimodal_embedding\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.multimodal_embedding.embedding_content_part_image_param',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\multimodal_embedding\\embedding_content_part_image_param.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.multimodal_embedding.embedding_content_part_text_param',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\multimodal_embedding\\embedding_content_part_text_param.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.multimodal_embedding.embedding_data',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\multimodal_embedding\\embedding_data.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.multimodal_embedding.embedding_input',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\multimodal_embedding\\embedding_input.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.multimodal_embedding.embedding_response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\multimodal_embedding\\embedding_response.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.multimodal_embedding.embedding_usage',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\multimodal_embedding\\embedding_usage.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.shared',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\shared\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.shared.response_format_json_object',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\shared\\response_format_json_object.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.shared.response_format_json_schema',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\shared\\response_format_json_schema.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.shared.response_format_text',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\shared\\response_format_text.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.shared_params',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\shared_params\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.shared_params.function_definition',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\shared_params\\function_definition.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.shared_params.function_parameters',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\shared_params\\function_parameters.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.shared_params.metadata',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\shared_params\\metadata.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.shared_params.response_format_json_object',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\shared_params\\response_format_json_object.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.shared_params.response_format_json_schema',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\shared_params\\response_format_json_schema.py',
   'PYMODULE'),
  ('volcenginesdkarkruntime.types.shared_params.response_format_text',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkarkruntime\\types\\shared_params\\response_format_text.py',
   'PYMODULE'),
  ('volcenginesdkcore',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkcore.api_client',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\api_client.py',
   'PYMODULE'),
  ('volcenginesdkcore.configuration',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\configuration.py',
   'PYMODULE'),
  ('volcenginesdkcore.endpoint',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\endpoint\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkcore.endpoint.endpoint_provider',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\endpoint\\endpoint_provider.py',
   'PYMODULE'),
  ('volcenginesdkcore.endpoint.providers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\endpoint\\providers\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkcore.endpoint.providers.default_provider',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\endpoint\\providers\\default_provider.py',
   'PYMODULE'),
  ('volcenginesdkcore.flatten',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\flatten.py',
   'PYMODULE'),
  ('volcenginesdkcore.interceptor',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\interceptor\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkcore.interceptor.chain',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\interceptor\\chain.py',
   'PYMODULE'),
  ('volcenginesdkcore.interceptor.interceptors',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\interceptor\\interceptors\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkcore.interceptor.interceptors.build_request_interceptor',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\interceptor\\interceptors\\build_request_interceptor.py',
   'PYMODULE'),
  ('volcenginesdkcore.interceptor.interceptors.context',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\interceptor\\interceptors\\context.py',
   'PYMODULE'),
  ('volcenginesdkcore.interceptor.interceptors.deserialized_response_interceptor',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\interceptor\\interceptors\\deserialized_response_interceptor.py',
   'PYMODULE'),
  ('volcenginesdkcore.interceptor.interceptors.interceptor',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\interceptor\\interceptors\\interceptor.py',
   'PYMODULE'),
  ('volcenginesdkcore.interceptor.interceptors.request',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\interceptor\\interceptors\\request.py',
   'PYMODULE'),
  ('volcenginesdkcore.interceptor.interceptors.resolve_endpoint_interceptor',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\interceptor\\interceptors\\resolve_endpoint_interceptor.py',
   'PYMODULE'),
  ('volcenginesdkcore.interceptor.interceptors.response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\interceptor\\interceptors\\response.py',
   'PYMODULE'),
  ('volcenginesdkcore.interceptor.interceptors.runtime_options_interceptor',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\interceptor\\interceptors\\runtime_options_interceptor.py',
   'PYMODULE'),
  ('volcenginesdkcore.interceptor.interceptors.sign_request_interceptor',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\interceptor\\interceptors\\sign_request_interceptor.py',
   'PYMODULE'),
  ('volcenginesdkcore.metadata',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\metadata.py',
   'PYMODULE'),
  ('volcenginesdkcore.rest',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\rest.py',
   'PYMODULE'),
  ('volcenginesdkcore.retryer',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\retryer\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkcore.retryer.backoff_strategy',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\retryer\\backoff_strategy.py',
   'PYMODULE'),
  ('volcenginesdkcore.retryer.retry_condition',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\retryer\\retry_condition.py',
   'PYMODULE'),
  ('volcenginesdkcore.retryer.retryer',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\retryer\\retryer.py',
   'PYMODULE'),
  ('volcenginesdkcore.signv4',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\signv4.py',
   'PYMODULE'),
  ('volcenginesdkcore.universal',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\universal.py',
   'PYMODULE'),
  ('volcenginesdkcore.utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\utils\\__init__.py',
   'PYMODULE'),
  ('volcenginesdkcore.utils.six_utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\volcenginesdkcore\\utils\\six_utils.py',
   'PYMODULE'),
  ('webbrowser', 'D:\\Python\\Python39-32\\lib\\webbrowser.py', 'PYMODULE'),
  ('xml', 'D:\\Python\\Python39-32\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.etree',
   'D:\\Python\\Python39-32\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\Python\\Python39-32\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\Python\\Python39-32\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\Python\\Python39-32\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\Python\\Python39-32\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\Python\\Python39-32\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Python\\Python39-32\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\Python\\Python39-32\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Python\\Python39-32\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Python\\Python39-32\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Python\\Python39-32\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Python\\Python39-32\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Python\\Python39-32\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'D:\\Python\\Python39-32\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client',
   'D:\\Python\\Python39-32\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('yaml',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('yaml.events',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('zipfile', 'D:\\Python\\Python39-32\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'D:\\Python\\Python39-32\\lib\\zipimport.py', 'PYMODULE'),
  ('zipp',
   'D:\\Python\\Python39-32\\lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp._functools',
   'D:\\Python\\Python39-32\\lib\\site-packages\\zipp\\_functools.py',
   'PYMODULE'),
  ('zipp.compat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   'D:\\Python\\Python39-32\\lib\\site-packages\\zipp\\compat\\overlay.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   'D:\\Python\\Python39-32\\lib\\site-packages\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('zipp.compat.py313',
   'D:\\Python\\Python39-32\\lib\\site-packages\\zipp\\compat\\py313.py',
   'PYMODULE'),
  ('zipp.glob',
   'D:\\Python\\Python39-32\\lib\\site-packages\\zipp\\glob.py',
   'PYMODULE'),
  ('zoneinfo',
   'D:\\Python\\Python39-32\\lib\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('zoneinfo._common',
   'D:\\Python\\Python39-32\\lib\\zoneinfo\\_common.py',
   'PYMODULE'),
  ('zoneinfo._tzpath',
   'D:\\Python\\Python39-32\\lib\\zoneinfo\\_tzpath.py',
   'PYMODULE'),
  ('zoneinfo._zoneinfo',
   'D:\\Python\\Python39-32\\lib\\zoneinfo\\_zoneinfo.py',
   'PYMODULE'),
  ('zstandard',
   'D:\\Python\\Python39-32\\lib\\site-packages\\zstandard\\__init__.py',
   'PYMODULE'),
  ('zstandard.backend_cffi',
   'D:\\Python\\Python39-32\\lib\\site-packages\\zstandard\\backend_cffi.py',
   'PYMODULE')])
