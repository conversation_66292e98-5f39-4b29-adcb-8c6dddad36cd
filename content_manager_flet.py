import os
import keyboard
import pyautogui
import pyperclip
import json
import logging
import flet as ft
from volcenginesdkarkruntime import Ark
import threading
import time
from datetime import datetime
global keywords_content
class ContentManagerFlet:
    def __init__(self, page: ft.Page):
        self.page = page
        self.page.title = "内容管理器"
        self.page.window_width = 400
        self.page.window_height = 240
        self.page.theme_mode = ft.ThemeMode.LIGHT
        self.page.window_resizable = False
        self.page.window_center = True
        self.page.padding = 5
        
        # 初始化日志
        self.setup_logging()
        
        # API客户端
        self.client = Ark(
            api_key="2fd46ed1-bba7-4c3f-adb3-306d2703bf02",
            base_url="https://ark.cn-beijing.volces.com/api/v3",
        )
        
        # 数据
        self.content_data = []
        self.current_index = 0
        self.is_running = False
        self.hotkey_registered = False
        self.is_fetching = False  # 防止重复获取的标志
        self.is_recording_hotkey = False  # 是否正在录制快捷键
        self.last_content_input = ""  # 记录上次获取内容时的输入内容
        
        # UI组件
        self.content_input = None
        self.hotkey_input = None
        self.status_text = None
        self.start_btn = None
        self.stop_btn = None
        
        # 创建UI
        self.setup_ui()
        
        self.log_message("程序启动", "INFO")
    
    def setup_logging(self):
        """设置日志记录"""
        # 创建logs目录
        if not os.path.exists('logs'):
            os.makedirs('logs')
        
        # 配置日志
        log_filename = f"logs/content_manager_{datetime.now().strftime('%Y%m%d')}.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def setup_ui(self):
        """创建UI界面"""
        # 设置页面属性
        self.page.scroll = None
        self.page.auto_scroll = False

        # 配置区域 - 水平排列
        config_row = ft.Row([
            ft.TextField(
                label="内容",
                value="魔芋爽",
                width=180,
                height=55,
                text_size=18
            ),
            ft.TextField(
                label="快捷键",
                value="f2",
                width=110,
                height=55,
                text_size=18,
                on_focus=self.start_hotkey_recording,
                on_blur=self.stop_hotkey_recording,
                read_only=True,
                hint_text="点击后按键录制"
            )
        ], spacing=10, alignment=ft.MainAxisAlignment.CENTER)

        # 按钮区域
        button_row = ft.Row([
            ft.ElevatedButton(
                "启动",
                on_click=self.start_listening,
                bgcolor="#4caf50",
                color="#ffffff",
                width=80,
                height=55
            ),
            ft.ElevatedButton(
                "停止",
                on_click=self.stop_listening,
                bgcolor="#f44336",
                color="#ffffff",
                width=80,
                height=55,
                disabled=True
            )
        ], spacing=10, alignment=ft.MainAxisAlignment.CENTER)

        # 状态
        self.status_text = ft.Text("未启动", size=15, text_align=ft.TextAlign.CENTER)

        # 主布局 - 紧凑排列
        main_column = ft.Column([
            ft.Text("内容管理器", size=14, weight=ft.FontWeight.BOLD, text_align=ft.TextAlign.CENTER),
            config_row,
            self.status_text,
            button_row,
        ], spacing=1, horizontal_alignment=ft.CrossAxisAlignment.CENTER, tight=True)

        # 添加到页面
        self.page.add(main_column)

        # 保存引用
        self.content_input = config_row.controls[0]
        self.hotkey_input = config_row.controls[1]
        self.start_btn = button_row.controls[0]
        self.stop_btn = button_row.controls[1]
    
    def log_message(self, message, level="INFO"):
        """记录日志消息到文件"""
        # 只写入日志文件，不在界面显示
        if level == "ERROR":
            self.logger.error(message)
        elif level == "WARNING":
            self.logger.warning(message)
        else:
            self.logger.info(message)
    
    def fetch_content(self):
        """从API获取内容"""
        content = self.content_input.value.strip()
        if not content:
            self.log_message("请输入API请求内容", "ERROR")
            return

        self.log_message(f"正在获取新内容，请求内容: {content}")
        try:
            completion = self.client.bot_chat.completions.create(
                model="bot-20250804120908-9xj2l",
                messages=[
                    {"role": "user", "content": content}
                ],
            )

            response_text = completion.choices[0].message.content
            new_data = self.parse_api_response(response_text)
            self.content_data.extend(new_data)

            # 更新记录的内容输入
            self.last_content_input = content

            self.log_message(f"成功获取{len(new_data)}条新内容，当前总数：{len(self.content_data)}")
            self.update_status()

        except Exception as e:
            self.log_message(f"获取内容失败：{e}", "ERROR")

    def start_hotkey_recording(self, e):
        """开始录制快捷键"""
        if not self.is_running:  # 只有在未运行时才能录制
            self.is_recording_hotkey = True
            self.hotkey_input.hint_text = "请按下要设置的快捷键..."
            self.hotkey_input.bgcolor = "#fff3e0"  # 橙色背景提示录制中

            # 更新状态显示
            self.status_text.value = "请按下要设置的快捷键..."
            self.status_text.color = "#ff9800"  # 橙色
            self.page.update()

            # 启动键盘监听来录制快捷键
            threading.Thread(target=self.record_hotkey, daemon=True).start()
            self.log_message("开始录制快捷键，请按下要设置的键...")

    def stop_hotkey_recording(self, e):
        """停止录制快捷键"""
        if self.is_recording_hotkey:
            self.is_recording_hotkey = False
            self.hotkey_input.bgcolor = "#f5f5f5"  # 恢复原背景色
            self.hotkey_input.hint_text = "点击后按键录制"

            # 恢复状态显示
            self.status_text.value = "未启动"
            self.status_text.color = "#616161"
            self.page.update()

    def record_hotkey(self):
        """录制快捷键的线程函数"""
        try:
            # 等待按键事件
            event = keyboard.read_event()
            if event.event_type == keyboard.KEY_DOWN and self.is_recording_hotkey:
                key_name = event.name

                # 处理特殊键名
                if key_name == 'space':
                    key_name = 'space'
                elif key_name == 'enter':
                    key_name = 'enter'
                elif key_name == 'tab':
                    key_name = 'tab'
                elif key_name == 'esc':
                    key_name = 'esc'
                elif key_name == 'ctrl':
                    return  # 忽略单独的ctrl键
                elif key_name == 'alt':
                    return  # 忽略单独的alt键
                elif key_name == 'shift':
                    return  # 忽略单独的shift键

                # 更新输入框
                self.hotkey_input.value = key_name
                self.is_recording_hotkey = False
                self.hotkey_input.bgcolor = "#f5f5f5"
                self.hotkey_input.hint_text = "点击后按键录制"

                # 更新状态显示
                self.status_text.value = f"已录制快捷键: {key_name}"
                self.status_text.color = "#4caf50"  # 绿色表示成功
                self.page.update()

                self.log_message(f"录制到快捷键: {key_name}")

                # 2秒后恢复状态
                threading.Timer(2.0, self.reset_status_after_recording).start()

        except Exception as e:
            self.log_message(f"录制快捷键失败: {e}", "ERROR")
            self.is_recording_hotkey = False

    def reset_status_after_recording(self):
        """录制完成后重置状态显示"""
        if not self.is_running:  # 只有在未运行时才重置
            self.status_text.value = "未启动"
            self.status_text.color = "#616161"
            self.page.update()

    def fetch_content_with_lock(self):
        """带锁的获取内容方法，防止重复获取"""
        try:
            self.fetch_content()
        finally:
            self.is_fetching = False

    def fetch_content_with_initialization(self):
        """初始化时的获取内容方法，会更新状态显示"""
        try:
            self.fetch_content()

            # 获取成功后更新状态
            if self.content_data:
                hotkey = self.hotkey_input.value.strip()
                self.status_text.value = f"初始化成功，监听中 - 快捷键: {hotkey}"
                self.status_text.color = "#388e3c"  # 绿色
                self.page.update()
                self.log_message("初始化成功，开始监听快捷键")
            else:
                self.status_text.value = "初始化失败，请重试"
                self.status_text.color = "#f44336"  # 红色
                self.page.update()

        except Exception as e:
            self.status_text.value = "初始化失败，请重试"
            self.status_text.color = "#f44336"  # 红色
            self.page.update()
            self.log_message(f"初始化失败：{e}", "ERROR")
        finally:
            self.is_fetching = False

    def parse_api_response(self, response_text):
        """解析API返回的内容"""
        data = []
        try:
            self.log_message(f"原始API响应长度: {len(response_text)} 字符")
            
            parsed = json.loads(response_text)
            
            if isinstance(parsed, list):
                content_list = parsed
            elif isinstance(parsed, dict):
                content_list = parsed.get("content", [])
                if not content_list:
                    content_list = parsed.get("题目与文案", [])
            else:
                content_list = []
            
            self.log_message(f"找到数组，包含{len(content_list)}个项目")
            
            for i, item in enumerate(content_list):
                title = item.get('title', '') or item.get('题目', '')
                text_content = item.get('text', '') or item.get('文案', '')
                global keywords_content
                keywords_content = item.get('keywords', '') or item.get('关键词', '')

                # 组合完整文案：文本 + 关键词（不换行）
                full_content = f"{text_content}" if text_content else "无内容"
                
                data.append({
                    "title": title or "无标题",
                    "content": full_content
                })
            
            self.log_message(f"解析成功，获得{len(data)}条内容")
            return data
            
        except Exception as e:
            self.log_message(f"解析失败：{e}", "ERROR")
            return []
    def update_status(self):
        """更新状态显示"""
        # 简化状态更新，不显示内容数量
        pass

    def paste_next_content(self):
        """粘贴下一个内容"""
        # 检查是否需要补充内容（提前获取）
        remaining_items = len(self.content_data) - (self.current_index // 2)
        if remaining_items <= 3 and not self.is_fetching:  # 剩余条或更少时就开始获取，且当前没在获取
            self.log_message(f"剩余{remaining_items}条内容，开始补充...")
            self.is_fetching = True
            threading.Thread(target=self.fetch_content_with_lock, daemon=True).start()

        if not self.content_data:
            self.log_message("没有可用内容", "WARNING")
            return

        item_index = self.current_index // 2
        is_title = self.current_index % 2 == 0

        if item_index >= len(self.content_data):
            self.log_message("内容已用完，尝试获取新内容...", "WARNING")
            threading.Thread(target=self.fetch_content, daemon=True).start()
            if item_index >= len(self.content_data):
                self.log_message("仍然没有可用内容", "ERROR")
                return

        if is_title:
            text = self.content_data[item_index]["title"]
            self.log_message(f"粘贴题目{item_index + 1}: {text}")
            pyperclip.copy(text)
            time.sleep(0.05)
            keyboard.send('ctrl+v')
            self.log_message("✓ 已执行粘贴操作")
        else:
            text = self.content_data[item_index]["content"]
            self.log_message(f"粘贴文案{item_index + 1}: {text}")
            pyperclip.copy(text)
            time.sleep(0.05)
            keyboard.send('ctrl+v')
            # self.log_message("✓ 已执行粘贴操作")
            keywords = [word for word in keywords_content.split('#') if word]
            for i,keyword in enumerate(keywords):
                time.sleep(0.3)
                pyautogui.write('#', interval=0.1)  # 每个关键词前都添加#
                time.sleep(0.3)  
                pyperclip.copy(keyword)
                time.sleep(0.3)
                keyboard.send('ctrl+v')
                time.sleep(0.3)
                keyboard.send('enter')
                time.sleep(0.3)
                
        # 直接粘贴
        self.current_index += 1
        self.update_status()

    def start_listening(self, e):
        """启动快捷键监听"""
        hotkey = self.hotkey_input.value.strip()
        if not hotkey:
            self.log_message("请输入快捷键", "ERROR")
            return

        # 检查内容是否发生变化
        current_content = self.content_input.value.strip()
        content_changed = current_content != self.last_content_input

        try:
            # 移除之前的快捷键
            if self.hotkey_registered:
                keyboard.unhook_all_hotkeys()

            # 注册新的快捷键
            keyboard.add_hotkey(hotkey, self.paste_next_content)
            self.hotkey_registered = True
            self.is_running = True

            self.start_btn.disabled = True
            self.stop_btn.disabled = False

            self.log_message(f"开始监听快捷键: {hotkey}")

            # 如果内容发生变化，清空之前的数据并重新获取
            if content_changed:
                self.log_message(f"检测到内容变化: '{self.last_content_input}' -> '{current_content}'")
                self.content_data = []  # 清空之前的数据
                self.current_index = 0  # 重置索引
                self.last_content_input = current_content  # 更新记录的内容

            # 如果没有内容或内容发生了变化，需要获取新内容
            if not self.content_data and not self.is_fetching:
                # 显示初始化状态
                self.status_text.value = "正在初始化，请稍后..."
                self.status_text.color = "#ff9800"  # 橙色
                self.page.update()

                self.is_fetching = True
                threading.Thread(target=self.fetch_content_with_initialization, daemon=True).start()
            else:
                # 已有内容且内容未变化，直接开始监听
                if not content_changed:
                    self.log_message("内容未变化，使用现有数据")
                self.status_text.value = f"监听中 - 快捷键: {hotkey}"
                self.status_text.color = "#388e3c"
                self.page.update()

        except Exception as e:
            self.log_message(f"启动监听失败：{e}", "ERROR")

    def stop_listening(self, e):
        """停止快捷键监听"""
        try:
            if self.hotkey_registered:
                keyboard.unhook_all_hotkeys()
                self.hotkey_registered = False

            self.is_running = False
            self.start_btn.disabled = False
            self.stop_btn.disabled = True
            self.status_text.value = "已停止"
            self.status_text.color = "#d32f2f"

            self.log_message("已停止监听")
            self.page.update()

        except Exception as e:
            self.log_message(f"停止监听失败：{e}", "ERROR")



def main(page: ft.Page):
    """主函数"""
    # 强制设置窗口大小
    page.window.width = 400
    page.window.height = 240
    page.window.resizable = False
    page.window.center()
    page.update()

    app = ContentManagerFlet(page)

if __name__ == "__main__":
    ft.app(target=main, view=ft.AppView.FLET_APP)
